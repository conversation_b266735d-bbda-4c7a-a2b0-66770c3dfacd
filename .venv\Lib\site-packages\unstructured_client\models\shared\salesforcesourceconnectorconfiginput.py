"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import List
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class SalesforceSourceConnectorConfigInputTypedDict(TypedDict):
    categories: List[str]
    consumer_key: str
    private_key: str
    username: str


class SalesforceSourceConnectorConfigInput(BaseModel):
    categories: List[str]

    consumer_key: str

    private_key: str

    username: str
