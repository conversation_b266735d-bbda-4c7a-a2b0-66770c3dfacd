"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class QdrantCloudDestinationConnectorConfigInputTypedDict(TypedDict):
    api_key: str
    collection_name: str
    url: str
    batch_size: NotRequired[int]


class QdrantCloudDestinationConnectorConfigInput(BaseModel):
    api_key: str

    collection_name: str

    url: str

    batch_size: Optional[int] = 50
