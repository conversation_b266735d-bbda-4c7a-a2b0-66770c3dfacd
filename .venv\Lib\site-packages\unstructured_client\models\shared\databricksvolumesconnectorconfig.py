"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
import pydantic
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.types import BaseModel


class DatabricksVolumesConnectorConfigTypedDict(TypedDict):
    catalog: str
    client_id: str
    client_secret: str
    host: str
    volume: str
    volume_path: str
    schema_: NotRequired[str]


class DatabricksVolumesConnectorConfig(BaseModel):
    catalog: str

    client_id: str

    client_secret: str

    host: str

    volume: str

    volume_path: str

    schema_: Annotated[Optional[str], pydantic.Field(alias="schema")] = "default"
