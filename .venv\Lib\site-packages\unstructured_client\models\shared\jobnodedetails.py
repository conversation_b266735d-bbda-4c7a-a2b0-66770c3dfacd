"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from pydantic import model_serializer
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class JobNodeDetailsTypedDict(TypedDict):
    failure: int
    in_progress: int
    ready: int
    success: int
    node_name: NotRequired[Nullable[str]]
    node_subtype: NotRequired[Nullable[str]]
    node_type: NotRequired[Nullable[str]]


class JobNodeDetails(BaseModel):
    failure: int

    in_progress: int

    ready: int

    success: int

    node_name: OptionalNullable[str] = UNSET

    node_subtype: OptionalNullable[str] = UNSET

    node_type: OptionalNullable[str] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["node_name", "node_subtype", "node_type"]
        nullable_fields = ["node_name", "node_subtype", "node_type"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
