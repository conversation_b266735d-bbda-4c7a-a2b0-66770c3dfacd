"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class DropboxSourceConnectorConfigInputTypedDict(TypedDict):
    remote_url: str
    token: str
    recursive: NotRequired[bool]


class DropboxSourceConnectorConfigInput(BaseModel):
    remote_url: str

    token: str

    recursive: Optional[bool] = True
