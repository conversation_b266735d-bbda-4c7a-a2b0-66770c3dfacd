"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .azuresourceconnectorconfig import (
    AzureSourceConnectorConfig,
    AzureSourceConnectorConfigTypedDict,
)
from .boxsourceconnectorconfig import (
    BoxSourceConnectorConfig,
    BoxSourceConnectorConfigTypedDict,
)
from .confluencesourceconnectorconfig import (
    ConfluenceSourceConnectorConfig,
    ConfluenceSourceConnectorConfigTypedDict,
)
from .couchbasesourceconnectorconfig import (
    CouchbaseSourceConnectorConfig,
    CouchbaseSourceConnectorConfigTypedDict,
)
from .databricksvolumesconnectorconfig import (
    DatabricksVolumesConnectorConfig,
    DatabricksVolumesConnectorConfigTypedDict,
)
from .dropboxsourceconnectorconfig import (
    DropboxSourceConnectorConfig,
    DropboxSourceConnectorConfigTypedDict,
)
from .elasticsearchconnectorconfig import (
    ElasticsearchConnectorConfig,
    ElasticsearchConnectorConfigTypedDict,
)
from .gcssourceconnectorconfig import (
    GCSSourceConnectorConfig,
    GCSSourceConnectorConfigTypedDict,
)
from .googledrivesourceconnectorconfig import (
    GoogleDriveSourceConnectorConfig,
    GoogleDriveSourceConnectorConfigTypedDict,
)
from .jirasourceconnectorconfig import (
    JiraSourceConnectorConfig,
    JiraSourceConnectorConfigTypedDict,
)
from .kafkacloudsourceconnectorconfig import (
    KafkaCloudSourceConnectorConfig,
    KafkaCloudSourceConnectorConfigTypedDict,
)
from .mongodbconnectorconfig import (
    MongoDBConnectorConfig,
    MongoDBConnectorConfigTypedDict,
)
from .onedrivesourceconnectorconfig import (
    OneDriveSourceConnectorConfig,
    OneDriveSourceConnectorConfigTypedDict,
)
from .outlooksourceconnectorconfig import (
    OutlookSourceConnectorConfig,
    OutlookSourceConnectorConfigTypedDict,
)
from .postgressourceconnectorconfig import (
    PostgresSourceConnectorConfig,
    PostgresSourceConnectorConfigTypedDict,
)
from .s3sourceconnectorconfig import (
    S3SourceConnectorConfig,
    S3SourceConnectorConfigTypedDict,
)
from .salesforcesourceconnectorconfig import (
    SalesforceSourceConnectorConfig,
    SalesforceSourceConnectorConfigTypedDict,
)
from .sharepointsourceconnectorconfig import (
    SharePointSourceConnectorConfig,
    SharePointSourceConnectorConfigTypedDict,
)
from .snowflakesourceconnectorconfig import (
    SnowflakeSourceConnectorConfig,
    SnowflakeSourceConnectorConfigTypedDict,
)
from .sourceconnectortype import SourceConnectorType
from .zendesksourceconnectorconfig import (
    ZendeskSourceConnectorConfig,
    ZendeskSourceConnectorConfigTypedDict,
)
from datetime import datetime
from pydantic import model_serializer
from typing import Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


SourceConnectorInformationConfigTypedDict = TypeAliasType(
    "SourceConnectorInformationConfigTypedDict",
    Union[
        BoxSourceConnectorConfigTypedDict,
        MongoDBConnectorConfigTypedDict,
        DropboxSourceConnectorConfigTypedDict,
        ElasticsearchConnectorConfigTypedDict,
        GCSSourceConnectorConfigTypedDict,
        SalesforceSourceConnectorConfigTypedDict,
        GoogleDriveSourceConnectorConfigTypedDict,
        ZendeskSourceConnectorConfigTypedDict,
        AzureSourceConnectorConfigTypedDict,
        OneDriveSourceConnectorConfigTypedDict,
        KafkaCloudSourceConnectorConfigTypedDict,
        DatabricksVolumesConnectorConfigTypedDict,
        OutlookSourceConnectorConfigTypedDict,
        S3SourceConnectorConfigTypedDict,
        SharePointSourceConnectorConfigTypedDict,
        CouchbaseSourceConnectorConfigTypedDict,
        PostgresSourceConnectorConfigTypedDict,
        JiraSourceConnectorConfigTypedDict,
        ConfluenceSourceConnectorConfigTypedDict,
        SnowflakeSourceConnectorConfigTypedDict,
    ],
)


SourceConnectorInformationConfig = TypeAliasType(
    "SourceConnectorInformationConfig",
    Union[
        BoxSourceConnectorConfig,
        MongoDBConnectorConfig,
        DropboxSourceConnectorConfig,
        ElasticsearchConnectorConfig,
        GCSSourceConnectorConfig,
        SalesforceSourceConnectorConfig,
        GoogleDriveSourceConnectorConfig,
        ZendeskSourceConnectorConfig,
        AzureSourceConnectorConfig,
        OneDriveSourceConnectorConfig,
        KafkaCloudSourceConnectorConfig,
        DatabricksVolumesConnectorConfig,
        OutlookSourceConnectorConfig,
        S3SourceConnectorConfig,
        SharePointSourceConnectorConfig,
        CouchbaseSourceConnectorConfig,
        PostgresSourceConnectorConfig,
        JiraSourceConnectorConfig,
        ConfluenceSourceConnectorConfig,
        SnowflakeSourceConnectorConfig,
    ],
)


class SourceConnectorInformationTypedDict(TypedDict):
    config: SourceConnectorInformationConfigTypedDict
    created_at: datetime
    id: str
    name: str
    type: SourceConnectorType
    updated_at: NotRequired[Nullable[datetime]]


class SourceConnectorInformation(BaseModel):
    config: SourceConnectorInformationConfig

    created_at: datetime

    id: str

    name: str

    type: SourceConnectorType

    updated_at: OptionalNullable[datetime] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["updated_at"]
        nullable_fields = ["updated_at"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
