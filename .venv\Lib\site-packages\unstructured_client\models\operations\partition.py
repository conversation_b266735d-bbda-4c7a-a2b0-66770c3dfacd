"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
import httpx
import pydantic
from pydantic import model_serializer
from typing import Any, Dict, List, Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.models.shared import (
    partition_parameters as shared_partition_parameters,
)
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)
from unstructured_client.utils import FieldMetadata, HeaderMetadata, RequestMetadata

PARTITION_SERVER_SAAS_API = "saas-api"
r"""Serverless SaaS API"""

PARTITION_SERVER_DEVELOPMENT = "development"
r"""Development server"""

PARTITION_SERVERS = {
    PARTITION_SERVER_SAAS_API: "https://api.unstructuredapp.io",
    PARTITION_SERVER_DEVELOPMENT: "http://localhost:8000",
}


class PartitionRequestTypedDict(TypedDict):
    partition_parameters: shared_partition_parameters.PartitionParametersTypedDict
    unstructured_api_key: NotRequired[Nullable[str]]


class PartitionRequest(BaseModel):
    partition_parameters: Annotated[
        shared_partition_parameters.PartitionParameters,
        FieldMetadata(request=RequestMetadata(media_type="multipart/form-data")),
    ]

    unstructured_api_key: Annotated[
        OptionalNullable[str],
        pydantic.Field(alias="unstructured-api-key"),
        FieldMetadata(header=HeaderMetadata(style="simple", explode=False)),
    ] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["unstructured-api-key"]
        nullable_fields = ["unstructured-api-key"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m


class PartitionResponseTypedDict(TypedDict):
    content_type: str
    r"""HTTP response content type for this operation"""
    status_code: int
    r"""HTTP response status code for this operation"""
    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""
    csv_elements: NotRequired[str]
    r"""Successful Response"""
    elements: NotRequired[List[Dict[str, Any]]]
    r"""Successful Response"""


class PartitionResponse(BaseModel):
    content_type: str
    r"""HTTP response content type for this operation"""

    status_code: int
    r"""HTTP response status code for this operation"""

    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""

    csv_elements: Optional[str] = None
    r"""Successful Response"""

    elements: Optional[List[Dict[str, Any]]] = None
    r"""Successful Response"""
