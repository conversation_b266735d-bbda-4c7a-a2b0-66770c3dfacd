"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class QdrantCloudDestinationConnectorConfigTypedDict(TypedDict):
    api_key: str
    batch_size: int
    collection_name: str
    url: str


class QdrantCloudDestinationConnectorConfig(BaseModel):
    api_key: str

    batch_size: int

    collection_name: str

    url: str
