#!/usr/bin/env python3
"""
Setup script for PDF Extractor with Llama 3.2
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install Python requirements"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("Error: requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python requirements"
    )

def setup_ollama():
    """Setup Ollama and pull Llama 3.2 model"""
    print("\n" + "="*50)
    print("OLLAMA SETUP")
    print("="*50)
    
    # Check if Ollama is installed
    try:
        result = subprocess.run("ollama --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Ollama is already installed")
        else:
            print("Ollama not found. Please install Ollama:")
            print("- Visit: https://ollama.ai/")
            print("- Download and install for your OS")
            return False
    except FileNotFoundError:
        print("Ollama not found. Please install Ollama:")
        print("- Visit: https://ollama.ai/")
        print("- Download and install for your OS")
        return False
    
    # Pull Llama 3.2 model
    print("\nPulling Llama 3.2 model (this may take a while)...")
    return run_command("ollama pull llama3.2", "Pulling Llama 3.2 model")

def create_example_script():
    """Create an example usage script"""
    example_content = '''#!/usr/bin/env python3
"""
Example usage of PDF Extractor with Llama 3.2
"""

from pdf_extractor_llama import PDFExtractor
import json

def main():
    # Example PDF path - update this to your PDF file
    pdf_path = "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf"
    
    print("PDF Extractor with Llama 3.2 - Example Usage")
    print("=" * 50)
    
    # Initialize extractor (using Ollama by default)
    extractor = PDFExtractor(use_ollama=True)
    
    # Process the PDF
    result = extractor.process_pdf(pdf_path, "extracted_results.json")
    
    if "error" not in result:
        print("\\nExtraction completed successfully!")
        print(f"Text length: {result['extracted_text_length']} characters")
        print("\\nExtracted Information:")
        print(json.dumps(result["extracted_information"], indent=2))
    else:
        print(f"Error: {result['error']}")

if __name__ == "__main__":
    main()
'''
    
    with open("example_usage.py", "w", encoding="utf-8") as f:
        f.write(example_content)
    
    print("✓ Created example_usage.py")
    return True

def main():
    """Main setup function"""
    print("PDF Extractor with Llama 3.2 - Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("Failed to install requirements. Please check the error messages above.")
        sys.exit(1)
    
    # Setup Ollama (optional)
    print("\\nWould you like to setup Ollama for Llama 3.2? (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice in ['y', 'yes']:
        if not setup_ollama():
            print("\\nOllama setup failed. You can:")
            print("1. Install Ollama manually from https://ollama.ai/")
            print("2. Use transformers instead with --use-transformers flag")
    else:
        print("\\nSkipping Ollama setup. You can use transformers instead.")
    
    # Create example script
    create_example_script()
    
    print("\\n" + "=" * 50)
    print("SETUP COMPLETE!")
    print("=" * 50)
    print("\\nUsage examples:")
    print("1. Basic usage:")
    print("   python pdf_extractor_llama.py 'path/to/your/file.pdf'")
    print("\\n2. Save results to file:")
    print("   python pdf_extractor_llama.py 'path/to/your/file.pdf' -o results.json")
    print("\\n3. Use transformers instead of Ollama:")
    print("   python pdf_extractor_llama.py 'path/to/your/file.pdf' --use-transformers")
    print("\\n4. Run example:")
    print("   python example_usage.py")
    
    print("\\nNote: Make sure to update the PDF path in the scripts to point to your actual PDF file.")

if __name__ == "__main__":
    main()
