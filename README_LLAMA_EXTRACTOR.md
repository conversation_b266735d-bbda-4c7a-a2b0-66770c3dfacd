# PDF Information Extractor with Llama 3.2

A Python script that extracts text from PDFs using the unstructured library and then uses Llama 3.2 to extract specific information in JSON format.

## Features

- **PDF Text Extraction**: Uses unstructured library for high-quality text extraction
- **AI-Powered Information Extraction**: Uses Llama 3.2 to extract specific categories of information
- **Flexible LLM Backend**: Supports both Ollama and Transformers for running Llama 3.2
- **JSON Output**: Returns structured data in JSON format
- **Specific Categories**: Extracts predefined categories relevant to manufacturing/PPAP documents

## Extracted Categories

The script extracts the following information categories:

- Part Number
- Customer Part Number
- Engineering Change Level
- Date
- Purchase Order No.
- Weight
- Checking Aid No.
- Checking Aid Engineering Change Level
- Organization Manufacturing Information (Vendor Name, Code and Address)
- Customer Submission Information
- PO buyer name
- Reason for Submission
- Level of Submission
- PSW should be signed by supplier
- The Production Rate column should be filled out

## Installation

### Option 1: Automatic Setup (Recommended)

```bash
python setup_llama_extractor.py
```

This will:
1. Install all required Python packages
2. Optionally setup Ollama and pull Llama 3.2 model
3. Create example usage scripts

### Option 2: Manual Setup

1. Install Python requirements:
```bash
pip install -r requirements.txt
```

2. Setup Ollama (recommended):
```bash
# Install Ollama from https://ollama.ai/
# Then pull the Llama 3.2 model
ollama pull llama3.2
```

3. Alternative: Use Transformers (requires more memory):
```bash
# The script will automatically download the model when using --use-transformers
```

## Usage

### Basic Usage

```bash
# Extract information from a PDF
python pdf_extractor_llama.py "path/to/your/file.pdf"
```

### Save Results to File

```bash
# Save results to JSON file
python pdf_extractor_llama.py "path/to/your/file.pdf" -o results.json
```

### Use Transformers Instead of Ollama

```bash
# Use transformers backend (requires more memory but no Ollama installation)
python pdf_extractor_llama.py "path/to/your/file.pdf" --use-transformers
```

### Example with Your PDF

```bash
# Using the sample PDF in your project
python pdf_extractor_llama.py "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf" -o psw_results.json
```

## Programmatic Usage

```python
from pdf_extractor_llama import PDFExtractor
import json

# Initialize extractor
extractor = PDFExtractor(use_ollama=True)  # or use_ollama=False for transformers

# Process PDF
result = extractor.process_pdf("your_file.pdf", "output.json")

# Access extracted information
if "error" not in result:
    extracted_info = result["extracted_information"]
    print(json.dumps(extracted_info, indent=2))
```

## Output Format

The script returns a JSON object with the following structure:

```json
{
  "pdf_path": "path/to/file.pdf",
  "extracted_text_length": 5420,
  "extraction_method": "ollama",
  "extracted_information": {
    "Part Number": "P80009246A",
    "Customer Part Number": "ABC123",
    "Engineering Change Level": "Rev A",
    "Date": "2024-01-15",
    "Purchase Order No.": "PO123456",
    "Weight": "2.5 kg",
    "Checking Aid No.": null,
    "Checking Aid Engineering Change Level": null,
    "Organization Manufacturing Information (Vendor Name, Code and Address)": "XYZ Manufacturing, Code: 12345, Address: 123 Main St",
    "Customer Submission Information": "Initial submission",
    "PO buyer name": "John Smith",
    "Reason for Submission": "New part introduction",
    "Level of Submission": "Level 3",
    "PSW should be signed by supplier": "Yes",
    "The Production Rate column should be filled out": "1000 parts/month"
  }
}
```

## Requirements

- Python 3.8+
- unstructured library with all dependencies
- Either Ollama with Llama 3.2 model OR transformers with PyTorch

## Backend Options

### Ollama (Recommended)
- **Pros**: Easier to setup, lower memory usage, faster inference
- **Cons**: Requires separate Ollama installation
- **Setup**: Install Ollama from https://ollama.ai/ and run `ollama pull llama3.2`

### Transformers
- **Pros**: No external dependencies, integrated with Python
- **Cons**: Higher memory usage, slower first run (model download)
- **Setup**: Automatically downloads model on first use

## Troubleshooting

### Common Issues

1. **Ollama not found**
   - Install Ollama from https://ollama.ai/
   - Make sure Ollama service is running
   - Pull the model: `ollama pull llama3.2`

2. **Transformers out of memory**
   - Use Ollama instead: remove `--use-transformers` flag
   - Or use a smaller model variant

3. **PDF extraction fails**
   - Check if the PDF file exists and is readable
   - Try with a different PDF file
   - Check unstructured library installation

4. **Poor extraction quality**
   - Ensure the PDF has good text quality (not scanned images)
   - For scanned PDFs, unstructured will use OCR automatically

### Error Messages

- **"No text extracted from PDF"**: The PDF might be image-based or corrupted
- **"Ollama not available"**: Install Ollama and pull the llama3.2 model
- **"No valid JSON found in response"**: The LLM response was malformed, try again

## Performance Tips

1. **For better accuracy**: Use high-quality PDFs with clear text
2. **For faster processing**: Use Ollama instead of transformers
3. **For batch processing**: Process files one at a time to avoid memory issues

## License

This project uses open-source libraries. Check individual library licenses for details.
