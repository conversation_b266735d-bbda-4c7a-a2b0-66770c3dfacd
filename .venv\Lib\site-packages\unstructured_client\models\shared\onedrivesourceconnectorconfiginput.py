"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class OneDriveSourceConnectorConfigInputTypedDict(TypedDict):
    authority_url: str
    client_cred: str
    client_id: str
    path: str
    tenant: str
    user_pname: str
    recursive: NotRequired[bool]


class OneDriveSourceConnectorConfigInput(BaseModel):
    authority_url: str

    client_cred: str

    client_id: str

    path: str

    tenant: str

    user_pname: str

    recursive: Optional[bool] = False
