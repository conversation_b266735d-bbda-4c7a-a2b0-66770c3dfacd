#!/usr/bin/env python3
"""
Simple test script for PDF extractor
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if required libraries are available"""
    print("Testing imports...")
    
    try:
        import unstructured
        print("✓ unstructured library available")
    except ImportError as e:
        print(f"✗ unstructured library not available: {e}")
        return False
    
    try:
        from unstructured.partition.pdf import partition_pdf
        print("✓ PDF partition function available")
    except ImportError as e:
        print(f"✗ PDF partition function not available: {e}")
        return False
    
    # Test optional LLM libraries
    try:
        import ollama
        print("✓ ollama library available")
    except ImportError:
        print("⚠ ollama library not available (optional)")
    
    try:
        import transformers
        print("✓ transformers library available")
    except ImportError:
        print("⚠ transformers library not available (optional)")
    
    return True

def test_pdf_extraction():
    """Test PDF text extraction"""
    pdf_path = "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"✗ Test PDF not found: {pdf_path}")
        return False
    
    print(f"Testing PDF extraction with: {pdf_path}")
    
    try:
        from unstructured.partition.pdf import partition_pdf
        
        # Simple extraction test
        elements = partition_pdf(filename=pdf_path)
        
        # Get text content
        text_content = []
        for element in elements:
            if hasattr(element, 'text') and element.text.strip():
                text_content.append(element.text.strip())
        
        full_text = "\n".join(text_content)
        
        print(f"✓ Successfully extracted {len(full_text)} characters")
        print(f"✓ Found {len(elements)} elements")
        
        # Show first 200 characters as preview
        preview = full_text[:200] + "..." if len(full_text) > 200 else full_text
        print(f"Preview: {preview}")
        
        return True
        
    except Exception as e:
        print(f"✗ PDF extraction failed: {e}")
        return False

def test_basic_extractor():
    """Test the basic extractor functionality"""
    print("\nTesting basic extractor...")
    
    try:
        from pdf_extractor_llama import PDFExtractor
        
        # Create extractor without LLM (just for testing structure)
        extractor = PDFExtractor(use_ollama=True)
        
        print("✓ PDFExtractor class loaded successfully")
        print(f"✓ Categories defined: {len(extractor.categories)} items")
        
        return True
        
    except Exception as e:
        print(f"✗ PDFExtractor test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("PDF Extractor Test Suite")
    print("=" * 30)
    
    # Test imports
    if not test_imports():
        print("\n✗ Import tests failed. Please install required dependencies.")
        sys.exit(1)
    
    # Test PDF extraction
    if not test_pdf_extraction():
        print("\n✗ PDF extraction test failed.")
        sys.exit(1)
    
    # Test basic extractor
    if not test_basic_extractor():
        print("\n✗ Basic extractor test failed.")
        sys.exit(1)
    
    print("\n" + "=" * 30)
    print("✓ All tests passed!")
    print("=" * 30)
    
    print("\nYou can now use the extractor:")
    print("python pdf_extractor_llama.py 'FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf'")

if __name__ == "__main__":
    main()
