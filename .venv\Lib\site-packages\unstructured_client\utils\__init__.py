"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from typing import TYPE_CHECKING
from importlib import import_module

if TYPE_CHECKING:
    from .annotations import get_discriminator
    from .datetimes import parse_datetime
    from .enums import OpenEnumMeta
    from .headers import get_headers, get_response_headers
    from .metadata import (
        FieldMetadata,
        find_metadata,
        FormMetadata,
        HeaderMetadata,
        MultipartFormMetadata,
        PathParamMetadata,
        QueryParamMetadata,
        RequestMetadata,
        SecurityMetadata,
    )
    from .queryparams import get_query_params
    from .retries import BackoffStrategy, Retries, retry, retry_async, RetryConfig
    from .requestbodies import serialize_request_body, SerializedRequestBody
    from .security import get_security
    from .serializers import (
        get_pydantic_model,
        marshal_json,
        unmarshal,
        unmarshal_json,
        serialize_decimal,
        serialize_float,
        serialize_int,
        stream_to_text,
        stream_to_text_async,
        stream_to_bytes,
        stream_to_bytes_async,
        validate_const,
        validate_decimal,
        validate_float,
        validate_int,
        validate_open_enum,
    )
    from .url import generate_url, template_url, remove_suffix
    from .values import (
        get_global_from_env,
        match_content_type,
        match_status_codes,
        match_response,
        cast_partial,
    )
    from .logger import Logger, get_body_content, get_default_logger

__all__ = [
    "BackoffStrategy",
    "FieldMetadata",
    "find_metadata",
    "FormMetadata",
    "generate_url",
    "get_body_content",
    "get_default_logger",
    "get_discriminator",
    "parse_datetime",
    "get_global_from_env",
    "get_headers",
    "get_pydantic_model",
    "get_query_params",
    "get_response_headers",
    "get_security",
    "HeaderMetadata",
    "Logger",
    "marshal_json",
    "match_content_type",
    "match_status_codes",
    "match_response",
    "MultipartFormMetadata",
    "OpenEnumMeta",
    "PathParamMetadata",
    "QueryParamMetadata",
    "remove_suffix",
    "Retries",
    "retry",
    "retry_async",
    "RetryConfig",
    "RequestMetadata",
    "SecurityMetadata",
    "serialize_decimal",
    "serialize_float",
    "serialize_int",
    "serialize_request_body",
    "SerializedRequestBody",
    "stream_to_text",
    "stream_to_text_async",
    "stream_to_bytes",
    "stream_to_bytes_async",
    "template_url",
    "unmarshal",
    "unmarshal_json",
    "validate_decimal",
    "validate_const",
    "validate_float",
    "validate_int",
    "validate_open_enum",
    "cast_partial",
]

_dynamic_imports: dict[str, str] = {
    "BackoffStrategy": ".retries",
    "FieldMetadata": ".metadata",
    "find_metadata": ".metadata",
    "FormMetadata": ".metadata",
    "generate_url": ".url",
    "get_body_content": ".logger",
    "get_default_logger": ".logger",
    "get_discriminator": ".annotations",
    "parse_datetime": ".datetimes",
    "get_global_from_env": ".values",
    "get_headers": ".headers",
    "get_pydantic_model": ".serializers",
    "get_query_params": ".queryparams",
    "get_response_headers": ".headers",
    "get_security": ".security",
    "HeaderMetadata": ".metadata",
    "Logger": ".logger",
    "marshal_json": ".serializers",
    "match_content_type": ".values",
    "match_status_codes": ".values",
    "match_response": ".values",
    "MultipartFormMetadata": ".metadata",
    "OpenEnumMeta": ".enums",
    "PathParamMetadata": ".metadata",
    "QueryParamMetadata": ".metadata",
    "remove_suffix": ".url",
    "Retries": ".retries",
    "retry": ".retries",
    "retry_async": ".retries",
    "RetryConfig": ".retries",
    "RequestMetadata": ".metadata",
    "SecurityMetadata": ".metadata",
    "serialize_decimal": ".serializers",
    "serialize_float": ".serializers",
    "serialize_int": ".serializers",
    "serialize_request_body": ".requestbodies",
    "SerializedRequestBody": ".requestbodies",
    "stream_to_text": ".serializers",
    "stream_to_text_async": ".serializers",
    "stream_to_bytes": ".serializers",
    "stream_to_bytes_async": ".serializers",
    "template_url": ".url",
    "unmarshal": ".serializers",
    "unmarshal_json": ".serializers",
    "validate_decimal": ".serializers",
    "validate_const": ".serializers",
    "validate_float": ".serializers",
    "validate_int": ".serializers",
    "validate_open_enum": ".serializers",
    "cast_partial": ".values",
}


def __getattr__(attr_name: str) -> object:
    module_name = _dynamic_imports.get(attr_name)
    if module_name is None:
        raise AttributeError(
            f"no {attr_name} found in _dynamic_imports, module name -> {__name__} "
        )

    try:
        module = import_module(module_name, __package__)
        result = getattr(module, attr_name)
        return result
    except ImportError as e:
        raise ImportError(
            f"Failed to import {attr_name} from {module_name}: {e}"
        ) from e
    except AttributeError as e:
        raise AttributeError(
            f"Failed to get {attr_name} from {module_name}: {e}"
        ) from e


def __dir__():
    lazy_attrs = list(_dynamic_imports.keys())
    return sorted(lazy_attrs)
