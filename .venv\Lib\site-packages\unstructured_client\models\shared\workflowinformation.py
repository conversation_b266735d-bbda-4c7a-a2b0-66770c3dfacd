"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .workflownode import WorkflowNode, WorkflowNodeTypedDict
from .workflowschedule import WorkflowSchedule, WorkflowScheduleTypedDict
from .workflowstate import WorkflowState
from .workflowtype import WorkflowType
from datetime import datetime
from pydantic import model_serializer
from typing import List
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class WorkflowInformationTypedDict(TypedDict):
    created_at: datetime
    destinations: List[str]
    id: str
    name: str
    sources: List[str]
    status: WorkflowState
    workflow_nodes: List[WorkflowNodeTypedDict]
    reprocess_all: NotRequired[Nullable[bool]]
    schedule: NotRequired[Nullable[WorkflowScheduleTypedDict]]
    updated_at: NotRequired[Nullable[datetime]]
    workflow_type: NotRequired[Nullable[WorkflowType]]


class WorkflowInformation(BaseModel):
    created_at: datetime

    destinations: List[str]

    id: str

    name: str

    sources: List[str]

    status: WorkflowState

    workflow_nodes: List[WorkflowNode]

    reprocess_all: OptionalNullable[bool] = UNSET

    schedule: OptionalNullable[WorkflowSchedule] = UNSET

    updated_at: OptionalNullable[datetime] = UNSET

    workflow_type: OptionalNullable[WorkflowType] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["reprocess_all", "schedule", "updated_at", "workflow_type"]
        nullable_fields = ["reprocess_all", "schedule", "updated_at", "workflow_type"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
