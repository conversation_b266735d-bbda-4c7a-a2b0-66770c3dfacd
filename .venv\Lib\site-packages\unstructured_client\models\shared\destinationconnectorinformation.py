"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .astradbconnectorconfig import (
    AstraDBConnectorConfig,
    AstraDBConnectorConfigTypedDict,
)
from .azureaisearchconnectorconfig import (
    AzureAISearchConnectorConfig,
    AzureAISearchConnectorConfigTypedDict,
)
from .couchbasedestinationconnectorconfig import (
    CouchbaseDestinationConnectorConfig,
    CouchbaseDestinationConnectorConfigTypedDict,
)
from .databricksvdtdestinationconnectorconfig import (
    DatabricksVDTDestinationConnectorConfig,
    DatabricksVDTDestinationConnectorConfigTypedDict,
)
from .databricksvolumesconnectorconfig import (
    DatabricksVolumesConnectorConfig,
    DatabricksVolumesConnectorConfigTypedDict,
)
from .deltatableconnectorconfig import (
    DeltaTableConnectorConfig,
    DeltaTableConnectorConfigTypedDict,
)
from .destinationconnectortype import DestinationConnectorType
from .elasticsearchconnectorconfig import (
    ElasticsearchConnectorConfig,
    ElasticsearchConnectorConfigTypedDict,
)
from .gcsdestinationconnectorconfig import (
    GCSDestinationConnectorConfig,
    GCSDestinationConnectorConfigTypedDict,
)
from .ibmwatsonxs3destinationconnectorconfig import (
    IBMWatsonxS3DestinationConnectorConfig,
    IBMWatsonxS3DestinationConnectorConfigTypedDict,
)
from .kafkaclouddestinationconnectorconfig import (
    KafkaCloudDestinationConnectorConfig,
    KafkaCloudDestinationConnectorConfigTypedDict,
)
from .milvusdestinationconnectorconfig import (
    MilvusDestinationConnectorConfig,
    MilvusDestinationConnectorConfigTypedDict,
)
from .mongodbconnectorconfig import (
    MongoDBConnectorConfig,
    MongoDBConnectorConfigTypedDict,
)
from .neo4jdestinationconnectorconfig import (
    Neo4jDestinationConnectorConfig,
    Neo4jDestinationConnectorConfigTypedDict,
)
from .onedrivedestinationconnectorconfig import (
    OneDriveDestinationConnectorConfig,
    OneDriveDestinationConnectorConfigTypedDict,
)
from .pineconedestinationconnectorconfig import (
    PineconeDestinationConnectorConfig,
    PineconeDestinationConnectorConfigTypedDict,
)
from .postgresdestinationconnectorconfig import (
    PostgresDestinationConnectorConfig,
    PostgresDestinationConnectorConfigTypedDict,
)
from .qdrantclouddestinationconnectorconfig import (
    QdrantCloudDestinationConnectorConfig,
    QdrantCloudDestinationConnectorConfigTypedDict,
)
from .redisdestinationconnectorconfig import (
    RedisDestinationConnectorConfig,
    RedisDestinationConnectorConfigTypedDict,
)
from .s3destinationconnectorconfig import (
    S3DestinationConnectorConfig,
    S3DestinationConnectorConfigTypedDict,
)
from .snowflakedestinationconnectorconfig import (
    SnowflakeDestinationConnectorConfig,
    SnowflakeDestinationConnectorConfigTypedDict,
)
from .weaviatedestinationconnectorconfig import (
    WeaviateDestinationConnectorConfig,
    WeaviateDestinationConnectorConfigTypedDict,
)
from datetime import datetime
from pydantic import model_serializer
from typing import Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


DestinationConnectorInformationConfigTypedDict = TypeAliasType(
    "DestinationConnectorInformationConfigTypedDict",
    Union[
        GCSDestinationConnectorConfigTypedDict,
        ElasticsearchConnectorConfigTypedDict,
        AzureAISearchConnectorConfigTypedDict,
        WeaviateDestinationConnectorConfigTypedDict,
        MongoDBConnectorConfigTypedDict,
        DeltaTableConnectorConfigTypedDict,
        QdrantCloudDestinationConnectorConfigTypedDict,
        PineconeDestinationConnectorConfigTypedDict,
        AstraDBConnectorConfigTypedDict,
        Neo4jDestinationConnectorConfigTypedDict,
        OneDriveDestinationConnectorConfigTypedDict,
        S3DestinationConnectorConfigTypedDict,
        MilvusDestinationConnectorConfigTypedDict,
        DatabricksVolumesConnectorConfigTypedDict,
        PostgresDestinationConnectorConfigTypedDict,
        KafkaCloudDestinationConnectorConfigTypedDict,
        CouchbaseDestinationConnectorConfigTypedDict,
        RedisDestinationConnectorConfigTypedDict,
        DatabricksVDTDestinationConnectorConfigTypedDict,
        SnowflakeDestinationConnectorConfigTypedDict,
        IBMWatsonxS3DestinationConnectorConfigTypedDict,
    ],
)


DestinationConnectorInformationConfig = TypeAliasType(
    "DestinationConnectorInformationConfig",
    Union[
        GCSDestinationConnectorConfig,
        ElasticsearchConnectorConfig,
        AzureAISearchConnectorConfig,
        WeaviateDestinationConnectorConfig,
        MongoDBConnectorConfig,
        DeltaTableConnectorConfig,
        QdrantCloudDestinationConnectorConfig,
        PineconeDestinationConnectorConfig,
        AstraDBConnectorConfig,
        Neo4jDestinationConnectorConfig,
        OneDriveDestinationConnectorConfig,
        S3DestinationConnectorConfig,
        MilvusDestinationConnectorConfig,
        DatabricksVolumesConnectorConfig,
        PostgresDestinationConnectorConfig,
        KafkaCloudDestinationConnectorConfig,
        CouchbaseDestinationConnectorConfig,
        RedisDestinationConnectorConfig,
        DatabricksVDTDestinationConnectorConfig,
        SnowflakeDestinationConnectorConfig,
        IBMWatsonxS3DestinationConnectorConfig,
    ],
)


class DestinationConnectorInformationTypedDict(TypedDict):
    config: DestinationConnectorInformationConfigTypedDict
    created_at: datetime
    id: str
    name: str
    type: DestinationConnectorType
    updated_at: NotRequired[Nullable[datetime]]


class DestinationConnectorInformation(BaseModel):
    config: DestinationConnectorInformationConfig

    created_at: datetime

    id: str

    name: str

    type: DestinationConnectorType

    updated_at: OptionalNullable[datetime] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["updated_at"]
        nullable_fields = ["updated_at"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
