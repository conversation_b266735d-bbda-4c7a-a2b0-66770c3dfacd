"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .secretreference import SecretReference, SecretReferenceTypedDict
from pydantic import model_serializer
from typing import List, Optional, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


GoogleDriveSourceConnectorConfigInputServiceAccountKeyTypedDict = TypeAliasType(
    "GoogleDriveSourceConnectorConfigInputServiceAccountKeyTypedDict",
    Union[SecretReferenceTypedDict, str],
)


GoogleDriveSourceConnectorConfigInputServiceAccountKey = TypeAliasType(
    "GoogleDriveSourceConnectorConfigInputServiceAccountKey",
    Union[SecretReference, str],
)


class GoogleDriveSourceConnectorConfigInputTypedDict(TypedDict):
    drive_id: str
    service_account_key: GoogleDriveSourceConnectorConfigInputServiceAccountKeyTypedDict
    extensions: NotRequired[Nullable[List[str]]]
    recursive: NotRequired[bool]


class GoogleDriveSourceConnectorConfigInput(BaseModel):
    drive_id: str

    service_account_key: GoogleDriveSourceConnectorConfigInputServiceAccountKey

    extensions: OptionalNullable[List[str]] = UNSET

    recursive: Optional[bool] = True

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["extensions", "recursive"]
        nullable_fields = ["extensions"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
