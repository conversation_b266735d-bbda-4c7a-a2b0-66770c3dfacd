"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .astradbconnectorconfiginput import (
    AstraDBConnectorConfigInput,
    AstraDBConnectorConfigInputTypedDict,
)
from .azureaisearchconnectorconfiginput import (
    AzureAISearchConnectorConfigInput,
    AzureAISearchConnectorConfigInputTypedDict,
)
from .couchbasedestinationconnectorconfiginput import (
    CouchbaseDestinationConnectorConfigInput,
    CouchbaseDestinationConnectorConfigInputTypedDict,
)
from .databricksvdtdestinationconnectorconfiginput import (
    DatabricksVDTDestinationConnectorConfigInput,
    DatabricksVDTDestinationConnectorConfigInputTypedDict,
)
from .databricksvolumesconnectorconfiginput import (
    DatabricksVolumesConnectorConfigInput,
    DatabricksVolumesConnectorConfigInputTypedDict,
)
from .deltatableconnectorconfiginput import (
    DeltaTableConnectorConfigInput,
    DeltaTableConnectorConfigInputTypedDict,
)
from .destinationconnectortype import DestinationConnectorType
from .elasticsearchconnectorconfiginput import (
    ElasticsearchConnectorConfigInput,
    ElasticsearchConnectorConfigInputTypedDict,
)
from .gcsdestinationconnectorconfiginput import (
    GCSDestinationConnectorConfigInput,
    GCSDestinationConnectorConfigInputTypedDict,
)
from .ibmwatsonxs3destinationconnectorconfiginput import (
    IBMWatsonxS3DestinationConnectorConfigInput,
    IBMWatsonxS3DestinationConnectorConfigInputTypedDict,
)
from .kafkaclouddestinationconnectorconfiginput import (
    KafkaCloudDestinationConnectorConfigInput,
    KafkaCloudDestinationConnectorConfigInputTypedDict,
)
from .milvusdestinationconnectorconfiginput import (
    MilvusDestinationConnectorConfigInput,
    MilvusDestinationConnectorConfigInputTypedDict,
)
from .mongodbconnectorconfiginput import (
    MongoDBConnectorConfigInput,
    MongoDBConnectorConfigInputTypedDict,
)
from .neo4jdestinationconnectorconfiginput import (
    Neo4jDestinationConnectorConfigInput,
    Neo4jDestinationConnectorConfigInputTypedDict,
)
from .onedrivedestinationconnectorconfiginput import (
    OneDriveDestinationConnectorConfigInput,
    OneDriveDestinationConnectorConfigInputTypedDict,
)
from .pineconedestinationconnectorconfiginput import (
    PineconeDestinationConnectorConfigInput,
    PineconeDestinationConnectorConfigInputTypedDict,
)
from .postgresdestinationconnectorconfiginput import (
    PostgresDestinationConnectorConfigInput,
    PostgresDestinationConnectorConfigInputTypedDict,
)
from .qdrantclouddestinationconnectorconfiginput import (
    QdrantCloudDestinationConnectorConfigInput,
    QdrantCloudDestinationConnectorConfigInputTypedDict,
)
from .redisdestinationconnectorconfiginput import (
    RedisDestinationConnectorConfigInput,
    RedisDestinationConnectorConfigInputTypedDict,
)
from .s3destinationconnectorconfiginput import (
    S3DestinationConnectorConfigInput,
    S3DestinationConnectorConfigInputTypedDict,
)
from .snowflakedestinationconnectorconfiginput import (
    SnowflakeDestinationConnectorConfigInput,
    SnowflakeDestinationConnectorConfigInputTypedDict,
)
from .weaviatedestinationconnectorconfiginput import (
    WeaviateDestinationConnectorConfigInput,
    WeaviateDestinationConnectorConfigInputTypedDict,
)
from typing import Union
from typing_extensions import TypeAliasType, TypedDict
from unstructured_client.types import BaseModel


ConfigTypedDict = TypeAliasType(
    "ConfigTypedDict",
    Union[
        GCSDestinationConnectorConfigInputTypedDict,
        ElasticsearchConnectorConfigInputTypedDict,
        AzureAISearchConnectorConfigInputTypedDict,
        WeaviateDestinationConnectorConfigInputTypedDict,
        MongoDBConnectorConfigInputTypedDict,
        DeltaTableConnectorConfigInputTypedDict,
        QdrantCloudDestinationConnectorConfigInputTypedDict,
        PineconeDestinationConnectorConfigInputTypedDict,
        Neo4jDestinationConnectorConfigInputTypedDict,
        OneDriveDestinationConnectorConfigInputTypedDict,
        S3DestinationConnectorConfigInputTypedDict,
        AstraDBConnectorConfigInputTypedDict,
        MilvusDestinationConnectorConfigInputTypedDict,
        DatabricksVolumesConnectorConfigInputTypedDict,
        PostgresDestinationConnectorConfigInputTypedDict,
        KafkaCloudDestinationConnectorConfigInputTypedDict,
        CouchbaseDestinationConnectorConfigInputTypedDict,
        RedisDestinationConnectorConfigInputTypedDict,
        DatabricksVDTDestinationConnectorConfigInputTypedDict,
        SnowflakeDestinationConnectorConfigInputTypedDict,
        IBMWatsonxS3DestinationConnectorConfigInputTypedDict,
    ],
)


Config = TypeAliasType(
    "Config",
    Union[
        GCSDestinationConnectorConfigInput,
        ElasticsearchConnectorConfigInput,
        AzureAISearchConnectorConfigInput,
        WeaviateDestinationConnectorConfigInput,
        MongoDBConnectorConfigInput,
        DeltaTableConnectorConfigInput,
        QdrantCloudDestinationConnectorConfigInput,
        PineconeDestinationConnectorConfigInput,
        Neo4jDestinationConnectorConfigInput,
        OneDriveDestinationConnectorConfigInput,
        S3DestinationConnectorConfigInput,
        AstraDBConnectorConfigInput,
        MilvusDestinationConnectorConfigInput,
        DatabricksVolumesConnectorConfigInput,
        PostgresDestinationConnectorConfigInput,
        KafkaCloudDestinationConnectorConfigInput,
        CouchbaseDestinationConnectorConfigInput,
        RedisDestinationConnectorConfigInput,
        DatabricksVDTDestinationConnectorConfigInput,
        SnowflakeDestinationConnectorConfigInput,
        IBMWatsonxS3DestinationConnectorConfigInput,
    ],
)


class CreateDestinationConnectorTypedDict(TypedDict):
    config: ConfigTypedDict
    name: str
    type: DestinationConnectorType


class CreateDestinationConnector(BaseModel):
    config: Config

    name: str

    type: DestinationConnectorType
