"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
import httpx
import pydantic
from pydantic import model_serializer
from typing import List, Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.models.shared import (
    sortdirection as shared_sortdirection,
    workflowinformation as shared_workflowinformation,
    workflowstate as shared_workflowstate,
)
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)
from unstructured_client.utils import FieldMetadata, HeaderMetadata, QueryParamMetadata

LIST_WORKFLOWS_SERVER_PLATFORM_API = "platform-api"
r"""Unstructured Platform API"""

LIST_WORKFLOWS_SERVERS = {
    LIST_WORKFLOWS_SERVER_PLATFORM_API: "https://platform.unstructuredapp.io/",
}


class ListWorkflowsRequestTypedDict(TypedDict):
    created_before: NotRequired[Nullable[datetime]]
    created_since: NotRequired[Nullable[datetime]]
    dag_node_configuration_id: NotRequired[Nullable[str]]
    destination_id: NotRequired[Nullable[str]]
    name: NotRequired[Nullable[str]]
    page: NotRequired[Nullable[int]]
    page_size: NotRequired[Nullable[int]]
    show_only_soft_deleted: NotRequired[Nullable[bool]]
    show_recommender_workflows: NotRequired[Nullable[bool]]
    sort_by: NotRequired[str]
    sort_direction: NotRequired[shared_sortdirection.SortDirection]
    source_id: NotRequired[Nullable[str]]
    status: NotRequired[Nullable[shared_workflowstate.WorkflowState]]
    unstructured_api_key: NotRequired[Nullable[str]]


class ListWorkflowsRequest(BaseModel):
    created_before: Annotated[
        OptionalNullable[datetime],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    created_since: Annotated[
        OptionalNullable[datetime],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    dag_node_configuration_id: Annotated[
        OptionalNullable[str],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    destination_id: Annotated[
        OptionalNullable[str],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    name: Annotated[
        OptionalNullable[str],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    page: Annotated[
        OptionalNullable[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    page_size: Annotated[
        OptionalNullable[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    show_only_soft_deleted: Annotated[
        OptionalNullable[bool],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    show_recommender_workflows: Annotated[
        OptionalNullable[bool],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    sort_by: Annotated[
        Optional[str],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = "id"

    sort_direction: Annotated[
        Optional[shared_sortdirection.SortDirection],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = None

    source_id: Annotated[
        OptionalNullable[str],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    status: Annotated[
        OptionalNullable[shared_workflowstate.WorkflowState],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = UNSET

    unstructured_api_key: Annotated[
        OptionalNullable[str],
        pydantic.Field(alias="unstructured-api-key"),
        FieldMetadata(header=HeaderMetadata(style="simple", explode=False)),
    ] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "created_before",
            "created_since",
            "dag_node_configuration_id",
            "destination_id",
            "name",
            "page",
            "page_size",
            "show_only_soft_deleted",
            "show_recommender_workflows",
            "sort_by",
            "sort_direction",
            "source_id",
            "status",
            "unstructured-api-key",
        ]
        nullable_fields = [
            "created_before",
            "created_since",
            "dag_node_configuration_id",
            "destination_id",
            "name",
            "page",
            "page_size",
            "show_only_soft_deleted",
            "show_recommender_workflows",
            "source_id",
            "status",
            "unstructured-api-key",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m


class ListWorkflowsResponseTypedDict(TypedDict):
    content_type: str
    r"""HTTP response content type for this operation"""
    status_code: int
    r"""HTTP response status code for this operation"""
    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""
    response_list_workflows: NotRequired[
        List[shared_workflowinformation.WorkflowInformationTypedDict]
    ]
    r"""Successful Response"""


class ListWorkflowsResponse(BaseModel):
    content_type: str
    r"""HTTP response content type for this operation"""

    status_code: int
    r"""HTTP response status code for this operation"""

    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""

    response_list_workflows: Optional[
        List[shared_workflowinformation.WorkflowInformation]
    ] = None
    r"""Successful Response"""
