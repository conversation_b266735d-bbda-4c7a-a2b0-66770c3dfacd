"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class IBMWatsonxS3DestinationConnectorConfigInputTypedDict(TypedDict):
    access_key_id: str
    catalog: str
    iam_api_key: str
    iceberg_endpoint: str
    namespace: str
    object_storage_endpoint: str
    object_storage_region: str
    secret_access_key: str
    table: str
    max_retries: NotRequired[int]
    r"""Maximum number of retries to upload data"""
    max_retries_connection: NotRequired[int]
    r"""Maximum number of retries in case of a connection error"""
    record_id_key: NotRequired[str]
    r"""Searchable key to find entries for the same record on previous runs"""


class IBMWatsonxS3DestinationConnectorConfigInput(BaseModel):
    access_key_id: str

    catalog: str

    iam_api_key: str

    iceberg_endpoint: str

    namespace: str

    object_storage_endpoint: str

    object_storage_region: str

    secret_access_key: str

    table: str

    max_retries: Optional[int] = 50
    r"""Maximum number of retries to upload data"""

    max_retries_connection: Optional[int] = 10
    r"""Maximum number of retries in case of a connection error"""

    record_id_key: Optional[str] = "record_id"
    r"""Searchable key to find entries for the same record on previous runs"""
