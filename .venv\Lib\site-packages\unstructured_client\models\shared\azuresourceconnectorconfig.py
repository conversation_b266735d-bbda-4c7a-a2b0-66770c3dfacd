"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from pydantic import model_serializer
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class AzureSourceConnectorConfigTypedDict(TypedDict):
    recursive: bool
    remote_url: str
    account_key: NotRequired[Nullable[str]]
    account_name: NotRequired[Nullable[str]]
    connection_string: NotRequired[Nullable[str]]
    sas_token: NotRequired[Nullable[str]]


class AzureSourceConnectorConfig(BaseModel):
    recursive: bool

    remote_url: str

    account_key: OptionalNullable[str] = UNSET

    account_name: OptionalNullable[str] = UNSET

    connection_string: OptionalNullable[str] = UNSET

    sas_token: OptionalNullable[str] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "account_key",
            "account_name",
            "connection_string",
            "sas_token",
        ]
        nullable_fields = [
            "account_key",
            "account_name",
            "connection_string",
            "sas_token",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
