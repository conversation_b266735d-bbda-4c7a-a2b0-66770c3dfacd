"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from typing import TYPE_CHECKING
from importlib import import_module

if TYPE_CHECKING:
    from .astradbconnectorconfig import (
        AstraDBConnectorConfig,
        AstraDBConnectorConfigTypedDict,
    )
    from .astradbconnectorconfiginput import (
        AstraDBConnectorConfigInput,
        AstraDBConnectorConfigInputTypedDict,
    )
    from .azureaisearchconnectorconfig import (
        AzureAISearchConnectorConfig,
        AzureAISearchConnectorConfigTypedDict,
    )
    from .azureaisearchconnectorconfiginput import (
        AzureAISearchConnectorConfigInput,
        AzureAISearchConnectorConfigInputTypedDict,
    )
    from .azuresourceconnectorconfig import (
        AzureSourceConnectorConfig,
        AzureSourceConnectorConfigTypedDict,
    )
    from .azuresourceconnectorconfiginput import (
        AzureSourceConnectorConfigInput,
        AzureSourceConnectorConfigInputTypedDict,
    )
    from .body_run_workflow import (
        BodyRunWorkflow,
        BodyRunWorkflowTypedDict,
        InputFiles,
        InputFilesTypedDict,
    )
    from .boxsourceconnectorconfig import (
        BoxSourceConnectorConfig,
        BoxSourceConnectorConfigTypedDict,
    )
    from .boxsourceconnectorconfiginput import (
        BoxSourceConnectorConfigInput,
        BoxSourceConnectorConfigInputTypedDict,
    )
    from .confluencesourceconnectorconfig import (
        ConfluenceSourceConnectorConfig,
        ConfluenceSourceConnectorConfigTypedDict,
    )
    from .confluencesourceconnectorconfiginput import (
        ConfluenceSourceConnectorConfigInput,
        ConfluenceSourceConnectorConfigInputTypedDict,
    )
    from .connectioncheckstatus import ConnectionCheckStatus
    from .couchbasedestinationconnectorconfig import (
        CouchbaseDestinationConnectorConfig,
        CouchbaseDestinationConnectorConfigTypedDict,
    )
    from .couchbasedestinationconnectorconfiginput import (
        CouchbaseDestinationConnectorConfigInput,
        CouchbaseDestinationConnectorConfigInputTypedDict,
    )
    from .couchbasesourceconnectorconfig import (
        CouchbaseSourceConnectorConfig,
        CouchbaseSourceConnectorConfigTypedDict,
    )
    from .couchbasesourceconnectorconfiginput import (
        CouchbaseSourceConnectorConfigInput,
        CouchbaseSourceConnectorConfigInputTypedDict,
    )
    from .createdestinationconnector import (
        Config,
        ConfigTypedDict,
        CreateDestinationConnector,
        CreateDestinationConnectorTypedDict,
    )
    from .createsourceconnector import (
        CreateSourceConnector,
        CreateSourceConnectorConfig,
        CreateSourceConnectorConfigTypedDict,
        CreateSourceConnectorTypedDict,
    )
    from .createworkflow import CreateWorkflow, CreateWorkflowTypedDict, Schedule
    from .crontabentry import CronTabEntry, CronTabEntryTypedDict
    from .dagnodeconnectioncheck import (
        DagNodeConnectionCheck,
        DagNodeConnectionCheckTypedDict,
    )
    from .databricksvdtdestinationconnectorconfig import (
        DatabricksVDTDestinationConnectorConfig,
        DatabricksVDTDestinationConnectorConfigTypedDict,
    )
    from .databricksvdtdestinationconnectorconfiginput import (
        DatabricksVDTDestinationConnectorConfigInput,
        DatabricksVDTDestinationConnectorConfigInputTypedDict,
    )
    from .databricksvolumesconnectorconfig import (
        DatabricksVolumesConnectorConfig,
        DatabricksVolumesConnectorConfigTypedDict,
    )
    from .databricksvolumesconnectorconfiginput import (
        DatabricksVolumesConnectorConfigInput,
        DatabricksVolumesConnectorConfigInputTypedDict,
    )
    from .deltatableconnectorconfig import (
        DeltaTableConnectorConfig,
        DeltaTableConnectorConfigTypedDict,
    )
    from .deltatableconnectorconfiginput import (
        DeltaTableConnectorConfigInput,
        DeltaTableConnectorConfigInputTypedDict,
    )
    from .destinationconnectorinformation import (
        DestinationConnectorInformation,
        DestinationConnectorInformationConfig,
        DestinationConnectorInformationConfigTypedDict,
        DestinationConnectorInformationTypedDict,
    )
    from .destinationconnectortype import DestinationConnectorType
    from .dropboxsourceconnectorconfig import (
        DropboxSourceConnectorConfig,
        DropboxSourceConnectorConfigTypedDict,
    )
    from .dropboxsourceconnectorconfiginput import (
        DropboxSourceConnectorConfigInput,
        DropboxSourceConnectorConfigInputTypedDict,
    )
    from .elasticsearchconnectorconfig import (
        ElasticsearchConnectorConfig,
        ElasticsearchConnectorConfigTypedDict,
    )
    from .elasticsearchconnectorconfiginput import (
        ElasticsearchConnectorConfigInput,
        ElasticsearchConnectorConfigInputTypedDict,
    )
    from .encryptedsecret import EncryptedSecret, EncryptedSecretTypedDict
    from .encryptiontype import EncryptionType
    from .failedfile import FailedFile, FailedFileTypedDict
    from .gcsdestinationconnectorconfig import (
        GCSDestinationConnectorConfig,
        GCSDestinationConnectorConfigTypedDict,
    )
    from .gcsdestinationconnectorconfiginput import (
        GCSDestinationConnectorConfigInput,
        GCSDestinationConnectorConfigInputTypedDict,
    )
    from .gcssourceconnectorconfig import (
        GCSSourceConnectorConfig,
        GCSSourceConnectorConfigTypedDict,
    )
    from .gcssourceconnectorconfiginput import (
        GCSSourceConnectorConfigInput,
        GCSSourceConnectorConfigInputTypedDict,
    )
    from .googledrivesourceconnectorconfig import (
        GoogleDriveSourceConnectorConfig,
        GoogleDriveSourceConnectorConfigTypedDict,
        ServiceAccountKey,
        ServiceAccountKeyTypedDict,
    )
    from .googledrivesourceconnectorconfiginput import (
        GoogleDriveSourceConnectorConfigInput,
        GoogleDriveSourceConnectorConfigInputServiceAccountKey,
        GoogleDriveSourceConnectorConfigInputServiceAccountKeyTypedDict,
        GoogleDriveSourceConnectorConfigInputTypedDict,
    )
    from .ibmwatsonxs3destinationconnectorconfig import (
        IBMWatsonxS3DestinationConnectorConfig,
        IBMWatsonxS3DestinationConnectorConfigTypedDict,
    )
    from .ibmwatsonxs3destinationconnectorconfiginput import (
        IBMWatsonxS3DestinationConnectorConfigInput,
        IBMWatsonxS3DestinationConnectorConfigInputTypedDict,
    )
    from .jirasourceconnectorconfig import (
        JiraSourceConnectorConfig,
        JiraSourceConnectorConfigTypedDict,
    )
    from .jirasourceconnectorconfiginput import (
        JiraSourceConnectorConfigInput,
        JiraSourceConnectorConfigInputTypedDict,
    )
    from .jobdetails import JobDetails, JobDetailsTypedDict
    from .jobfailedfiles import JobFailedFiles, JobFailedFilesTypedDict
    from .jobinformation import JobInformation, JobInformationTypedDict
    from .jobnodedetails import JobNodeDetails, JobNodeDetailsTypedDict
    from .jobprocessingstatus import JobProcessingStatus
    from .jobstatus import JobStatus
    from .kafkaclouddestinationconnectorconfig import (
        KafkaCloudDestinationConnectorConfig,
        KafkaCloudDestinationConnectorConfigTypedDict,
    )
    from .kafkaclouddestinationconnectorconfiginput import (
        KafkaCloudDestinationConnectorConfigInput,
        KafkaCloudDestinationConnectorConfigInputTypedDict,
    )
    from .kafkacloudsourceconnectorconfig import (
        KafkaCloudSourceConnectorConfig,
        KafkaCloudSourceConnectorConfigTypedDict,
    )
    from .kafkacloudsourceconnectorconfiginput import (
        KafkaCloudSourceConnectorConfigInput,
        KafkaCloudSourceConnectorConfigInputTypedDict,
    )
    from .milvusdestinationconnectorconfig import (
        MilvusDestinationConnectorConfig,
        MilvusDestinationConnectorConfigTypedDict,
    )
    from .milvusdestinationconnectorconfiginput import (
        MilvusDestinationConnectorConfigInput,
        MilvusDestinationConnectorConfigInputTypedDict,
    )
    from .mongodbconnectorconfig import (
        MongoDBConnectorConfig,
        MongoDBConnectorConfigTypedDict,
    )
    from .mongodbconnectorconfiginput import (
        MongoDBConnectorConfigInput,
        MongoDBConnectorConfigInputTypedDict,
    )
    from .neo4jdestinationconnectorconfig import (
        Neo4jDestinationConnectorConfig,
        Neo4jDestinationConnectorConfigTypedDict,
    )
    from .neo4jdestinationconnectorconfiginput import (
        Neo4jDestinationConnectorConfigInput,
        Neo4jDestinationConnectorConfigInputTypedDict,
    )
    from .nodefilemetadata import NodeFileMetadata, NodeFileMetadataTypedDict
    from .onedrivedestinationconnectorconfig import (
        OneDriveDestinationConnectorConfig,
        OneDriveDestinationConnectorConfigTypedDict,
    )
    from .onedrivedestinationconnectorconfiginput import (
        OneDriveDestinationConnectorConfigInput,
        OneDriveDestinationConnectorConfigInputTypedDict,
    )
    from .onedrivesourceconnectorconfig import (
        OneDriveSourceConnectorConfig,
        OneDriveSourceConnectorConfigTypedDict,
    )
    from .onedrivesourceconnectorconfiginput import (
        OneDriveSourceConnectorConfigInput,
        OneDriveSourceConnectorConfigInputTypedDict,
    )
    from .outlooksourceconnectorconfig import (
        OutlookSourceConnectorConfig,
        OutlookSourceConnectorConfigTypedDict,
    )
    from .outlooksourceconnectorconfiginput import (
        OutlookSourceConnectorConfigInput,
        OutlookSourceConnectorConfigInputTypedDict,
    )
    from .partition_parameters import (
        Files,
        FilesTypedDict,
        OutputFormat,
        PartitionParameters,
        PartitionParametersTypedDict,
        Strategy,
        VLMModel,
        VLMModelProvider,
    )
    from .pemauthresponse import PemAuthResponse, PemAuthResponseTypedDict
    from .pineconedestinationconnectorconfig import (
        PineconeDestinationConnectorConfig,
        PineconeDestinationConnectorConfigTypedDict,
    )
    from .pineconedestinationconnectorconfiginput import (
        PineconeDestinationConnectorConfigInput,
        PineconeDestinationConnectorConfigInputTypedDict,
    )
    from .postgresdestinationconnectorconfig import (
        PostgresDestinationConnectorConfig,
        PostgresDestinationConnectorConfigTypedDict,
    )
    from .postgresdestinationconnectorconfiginput import (
        PostgresDestinationConnectorConfigInput,
        PostgresDestinationConnectorConfigInputTypedDict,
    )
    from .postgressourceconnectorconfig import (
        PostgresSourceConnectorConfig,
        PostgresSourceConnectorConfigTypedDict,
    )
    from .postgressourceconnectorconfiginput import (
        PostgresSourceConnectorConfigInput,
        PostgresSourceConnectorConfigInputTypedDict,
    )
    from .qdrantclouddestinationconnectorconfig import (
        QdrantCloudDestinationConnectorConfig,
        QdrantCloudDestinationConnectorConfigTypedDict,
    )
    from .qdrantclouddestinationconnectorconfiginput import (
        QdrantCloudDestinationConnectorConfigInput,
        QdrantCloudDestinationConnectorConfigInputTypedDict,
    )
    from .redisdestinationconnectorconfig import (
        RedisDestinationConnectorConfig,
        RedisDestinationConnectorConfigTypedDict,
    )
    from .redisdestinationconnectorconfiginput import (
        RedisDestinationConnectorConfigInput,
        RedisDestinationConnectorConfigInputTypedDict,
    )
    from .s3destinationconnectorconfig import (
        S3DestinationConnectorConfig,
        S3DestinationConnectorConfigTypedDict,
    )
    from .s3destinationconnectorconfiginput import (
        S3DestinationConnectorConfigInput,
        S3DestinationConnectorConfigInputTypedDict,
    )
    from .s3sourceconnectorconfig import (
        S3SourceConnectorConfig,
        S3SourceConnectorConfigTypedDict,
    )
    from .s3sourceconnectorconfiginput import (
        S3SourceConnectorConfigInput,
        S3SourceConnectorConfigInputTypedDict,
    )
    from .salesforcesourceconnectorconfig import (
        SalesforceSourceConnectorConfig,
        SalesforceSourceConnectorConfigTypedDict,
    )
    from .salesforcesourceconnectorconfiginput import (
        SalesforceSourceConnectorConfigInput,
        SalesforceSourceConnectorConfigInputTypedDict,
    )
    from .secretreference import SecretReference, SecretReferenceTypedDict
    from .security import Security, SecurityTypedDict
    from .sharepointsourceconnectorconfig import (
        SharePointSourceConnectorConfig,
        SharePointSourceConnectorConfigTypedDict,
    )
    from .sharepointsourceconnectorconfiginput import (
        SharePointSourceConnectorConfigInput,
        SharePointSourceConnectorConfigInputTypedDict,
    )
    from .snowflakedestinationconnectorconfig import (
        SnowflakeDestinationConnectorConfig,
        SnowflakeDestinationConnectorConfigTypedDict,
    )
    from .snowflakedestinationconnectorconfiginput import (
        SnowflakeDestinationConnectorConfigInput,
        SnowflakeDestinationConnectorConfigInputTypedDict,
    )
    from .snowflakesourceconnectorconfig import (
        SnowflakeSourceConnectorConfig,
        SnowflakeSourceConnectorConfigTypedDict,
    )
    from .snowflakesourceconnectorconfiginput import (
        SnowflakeSourceConnectorConfigInput,
        SnowflakeSourceConnectorConfigInputTypedDict,
    )
    from .sortdirection import SortDirection
    from .sourceconnectorinformation import (
        SourceConnectorInformation,
        SourceConnectorInformationConfig,
        SourceConnectorInformationConfigTypedDict,
        SourceConnectorInformationTypedDict,
    )
    from .sourceconnectortype import SourceConnectorType
    from .updatedestinationconnector import (
        UpdateDestinationConnector,
        UpdateDestinationConnectorConfig,
        UpdateDestinationConnectorConfigTypedDict,
        UpdateDestinationConnectorTypedDict,
    )
    from .updatesourceconnector import (
        UpdateSourceConnector,
        UpdateSourceConnectorConfig,
        UpdateSourceConnectorConfigTypedDict,
        UpdateSourceConnectorTypedDict,
    )
    from .updateworkflow import (
        UpdateWorkflow,
        UpdateWorkflowSchedule,
        UpdateWorkflowTypedDict,
    )
    from .validationerror import (
        Loc,
        LocTypedDict,
        ValidationError,
        ValidationErrorTypedDict,
    )
    from .weaviatedestinationconnectorconfig import (
        WeaviateDestinationConnectorConfig,
        WeaviateDestinationConnectorConfigTypedDict,
    )
    from .weaviatedestinationconnectorconfiginput import (
        WeaviateDestinationConnectorConfigInput,
        WeaviateDestinationConnectorConfigInputTypedDict,
    )
    from .workflowinformation import WorkflowInformation, WorkflowInformationTypedDict
    from .workflowjobtype import WorkflowJobType
    from .workflownode import WorkflowNode, WorkflowNodeTypedDict
    from .workflowschedule import WorkflowSchedule, WorkflowScheduleTypedDict
    from .workflowstate import WorkflowState
    from .workflowtype import WorkflowType
    from .zendesksourceconnectorconfig import (
        ZendeskSourceConnectorConfig,
        ZendeskSourceConnectorConfigTypedDict,
    )
    from .zendesksourceconnectorconfiginput import (
        ZendeskSourceConnectorConfigInput,
        ZendeskSourceConnectorConfigInputTypedDict,
    )

__all__ = [
    "AstraDBConnectorConfig",
    "AstraDBConnectorConfigInput",
    "AstraDBConnectorConfigInputTypedDict",
    "AstraDBConnectorConfigTypedDict",
    "AzureAISearchConnectorConfig",
    "AzureAISearchConnectorConfigInput",
    "AzureAISearchConnectorConfigInputTypedDict",
    "AzureAISearchConnectorConfigTypedDict",
    "AzureSourceConnectorConfig",
    "AzureSourceConnectorConfigInput",
    "AzureSourceConnectorConfigInputTypedDict",
    "AzureSourceConnectorConfigTypedDict",
    "BodyRunWorkflow",
    "BodyRunWorkflowTypedDict",
    "BoxSourceConnectorConfig",
    "BoxSourceConnectorConfigInput",
    "BoxSourceConnectorConfigInputTypedDict",
    "BoxSourceConnectorConfigTypedDict",
    "Config",
    "ConfigTypedDict",
    "ConfluenceSourceConnectorConfig",
    "ConfluenceSourceConnectorConfigInput",
    "ConfluenceSourceConnectorConfigInputTypedDict",
    "ConfluenceSourceConnectorConfigTypedDict",
    "ConnectionCheckStatus",
    "CouchbaseDestinationConnectorConfig",
    "CouchbaseDestinationConnectorConfigInput",
    "CouchbaseDestinationConnectorConfigInputTypedDict",
    "CouchbaseDestinationConnectorConfigTypedDict",
    "CouchbaseSourceConnectorConfig",
    "CouchbaseSourceConnectorConfigInput",
    "CouchbaseSourceConnectorConfigInputTypedDict",
    "CouchbaseSourceConnectorConfigTypedDict",
    "CreateDestinationConnector",
    "CreateDestinationConnectorTypedDict",
    "CreateSourceConnector",
    "CreateSourceConnectorConfig",
    "CreateSourceConnectorConfigTypedDict",
    "CreateSourceConnectorTypedDict",
    "CreateWorkflow",
    "CreateWorkflowTypedDict",
    "CronTabEntry",
    "CronTabEntryTypedDict",
    "DagNodeConnectionCheck",
    "DagNodeConnectionCheckTypedDict",
    "DatabricksVDTDestinationConnectorConfig",
    "DatabricksVDTDestinationConnectorConfigInput",
    "DatabricksVDTDestinationConnectorConfigInputTypedDict",
    "DatabricksVDTDestinationConnectorConfigTypedDict",
    "DatabricksVolumesConnectorConfig",
    "DatabricksVolumesConnectorConfigInput",
    "DatabricksVolumesConnectorConfigInputTypedDict",
    "DatabricksVolumesConnectorConfigTypedDict",
    "DeltaTableConnectorConfig",
    "DeltaTableConnectorConfigInput",
    "DeltaTableConnectorConfigInputTypedDict",
    "DeltaTableConnectorConfigTypedDict",
    "DestinationConnectorInformation",
    "DestinationConnectorInformationConfig",
    "DestinationConnectorInformationConfigTypedDict",
    "DestinationConnectorInformationTypedDict",
    "DestinationConnectorType",
    "DropboxSourceConnectorConfig",
    "DropboxSourceConnectorConfigInput",
    "DropboxSourceConnectorConfigInputTypedDict",
    "DropboxSourceConnectorConfigTypedDict",
    "ElasticsearchConnectorConfig",
    "ElasticsearchConnectorConfigInput",
    "ElasticsearchConnectorConfigInputTypedDict",
    "ElasticsearchConnectorConfigTypedDict",
    "EncryptedSecret",
    "EncryptedSecretTypedDict",
    "EncryptionType",
    "FailedFile",
    "FailedFileTypedDict",
    "Files",
    "FilesTypedDict",
    "GCSDestinationConnectorConfig",
    "GCSDestinationConnectorConfigInput",
    "GCSDestinationConnectorConfigInputTypedDict",
    "GCSDestinationConnectorConfigTypedDict",
    "GCSSourceConnectorConfig",
    "GCSSourceConnectorConfigInput",
    "GCSSourceConnectorConfigInputTypedDict",
    "GCSSourceConnectorConfigTypedDict",
    "GoogleDriveSourceConnectorConfig",
    "GoogleDriveSourceConnectorConfigInput",
    "GoogleDriveSourceConnectorConfigInputServiceAccountKey",
    "GoogleDriveSourceConnectorConfigInputServiceAccountKeyTypedDict",
    "GoogleDriveSourceConnectorConfigInputTypedDict",
    "GoogleDriveSourceConnectorConfigTypedDict",
    "IBMWatsonxS3DestinationConnectorConfig",
    "IBMWatsonxS3DestinationConnectorConfigInput",
    "IBMWatsonxS3DestinationConnectorConfigInputTypedDict",
    "IBMWatsonxS3DestinationConnectorConfigTypedDict",
    "InputFiles",
    "InputFilesTypedDict",
    "JiraSourceConnectorConfig",
    "JiraSourceConnectorConfigInput",
    "JiraSourceConnectorConfigInputTypedDict",
    "JiraSourceConnectorConfigTypedDict",
    "JobDetails",
    "JobDetailsTypedDict",
    "JobFailedFiles",
    "JobFailedFilesTypedDict",
    "JobInformation",
    "JobInformationTypedDict",
    "JobNodeDetails",
    "JobNodeDetailsTypedDict",
    "JobProcessingStatus",
    "JobStatus",
    "KafkaCloudDestinationConnectorConfig",
    "KafkaCloudDestinationConnectorConfigInput",
    "KafkaCloudDestinationConnectorConfigInputTypedDict",
    "KafkaCloudDestinationConnectorConfigTypedDict",
    "KafkaCloudSourceConnectorConfig",
    "KafkaCloudSourceConnectorConfigInput",
    "KafkaCloudSourceConnectorConfigInputTypedDict",
    "KafkaCloudSourceConnectorConfigTypedDict",
    "Loc",
    "LocTypedDict",
    "MilvusDestinationConnectorConfig",
    "MilvusDestinationConnectorConfigInput",
    "MilvusDestinationConnectorConfigInputTypedDict",
    "MilvusDestinationConnectorConfigTypedDict",
    "MongoDBConnectorConfig",
    "MongoDBConnectorConfigInput",
    "MongoDBConnectorConfigInputTypedDict",
    "MongoDBConnectorConfigTypedDict",
    "Neo4jDestinationConnectorConfig",
    "Neo4jDestinationConnectorConfigInput",
    "Neo4jDestinationConnectorConfigInputTypedDict",
    "Neo4jDestinationConnectorConfigTypedDict",
    "NodeFileMetadata",
    "NodeFileMetadataTypedDict",
    "OneDriveDestinationConnectorConfig",
    "OneDriveDestinationConnectorConfigInput",
    "OneDriveDestinationConnectorConfigInputTypedDict",
    "OneDriveDestinationConnectorConfigTypedDict",
    "OneDriveSourceConnectorConfig",
    "OneDriveSourceConnectorConfigInput",
    "OneDriveSourceConnectorConfigInputTypedDict",
    "OneDriveSourceConnectorConfigTypedDict",
    "OutlookSourceConnectorConfig",
    "OutlookSourceConnectorConfigInput",
    "OutlookSourceConnectorConfigInputTypedDict",
    "OutlookSourceConnectorConfigTypedDict",
    "OutputFormat",
    "PartitionParameters",
    "PartitionParametersTypedDict",
    "PemAuthResponse",
    "PemAuthResponseTypedDict",
    "PineconeDestinationConnectorConfig",
    "PineconeDestinationConnectorConfigInput",
    "PineconeDestinationConnectorConfigInputTypedDict",
    "PineconeDestinationConnectorConfigTypedDict",
    "PostgresDestinationConnectorConfig",
    "PostgresDestinationConnectorConfigInput",
    "PostgresDestinationConnectorConfigInputTypedDict",
    "PostgresDestinationConnectorConfigTypedDict",
    "PostgresSourceConnectorConfig",
    "PostgresSourceConnectorConfigInput",
    "PostgresSourceConnectorConfigInputTypedDict",
    "PostgresSourceConnectorConfigTypedDict",
    "QdrantCloudDestinationConnectorConfig",
    "QdrantCloudDestinationConnectorConfigInput",
    "QdrantCloudDestinationConnectorConfigInputTypedDict",
    "QdrantCloudDestinationConnectorConfigTypedDict",
    "RedisDestinationConnectorConfig",
    "RedisDestinationConnectorConfigInput",
    "RedisDestinationConnectorConfigInputTypedDict",
    "RedisDestinationConnectorConfigTypedDict",
    "S3DestinationConnectorConfig",
    "S3DestinationConnectorConfigInput",
    "S3DestinationConnectorConfigInputTypedDict",
    "S3DestinationConnectorConfigTypedDict",
    "S3SourceConnectorConfig",
    "S3SourceConnectorConfigInput",
    "S3SourceConnectorConfigInputTypedDict",
    "S3SourceConnectorConfigTypedDict",
    "SalesforceSourceConnectorConfig",
    "SalesforceSourceConnectorConfigInput",
    "SalesforceSourceConnectorConfigInputTypedDict",
    "SalesforceSourceConnectorConfigTypedDict",
    "Schedule",
    "SecretReference",
    "SecretReferenceTypedDict",
    "Security",
    "SecurityTypedDict",
    "ServiceAccountKey",
    "ServiceAccountKeyTypedDict",
    "SharePointSourceConnectorConfig",
    "SharePointSourceConnectorConfigInput",
    "SharePointSourceConnectorConfigInputTypedDict",
    "SharePointSourceConnectorConfigTypedDict",
    "SnowflakeDestinationConnectorConfig",
    "SnowflakeDestinationConnectorConfigInput",
    "SnowflakeDestinationConnectorConfigInputTypedDict",
    "SnowflakeDestinationConnectorConfigTypedDict",
    "SnowflakeSourceConnectorConfig",
    "SnowflakeSourceConnectorConfigInput",
    "SnowflakeSourceConnectorConfigInputTypedDict",
    "SnowflakeSourceConnectorConfigTypedDict",
    "SortDirection",
    "SourceConnectorInformation",
    "SourceConnectorInformationConfig",
    "SourceConnectorInformationConfigTypedDict",
    "SourceConnectorInformationTypedDict",
    "SourceConnectorType",
    "Strategy",
    "UpdateDestinationConnector",
    "UpdateDestinationConnectorConfig",
    "UpdateDestinationConnectorConfigTypedDict",
    "UpdateDestinationConnectorTypedDict",
    "UpdateSourceConnector",
    "UpdateSourceConnectorConfig",
    "UpdateSourceConnectorConfigTypedDict",
    "UpdateSourceConnectorTypedDict",
    "UpdateWorkflow",
    "UpdateWorkflowSchedule",
    "UpdateWorkflowTypedDict",
    "VLMModel",
    "VLMModelProvider",
    "ValidationError",
    "ValidationErrorTypedDict",
    "WeaviateDestinationConnectorConfig",
    "WeaviateDestinationConnectorConfigInput",
    "WeaviateDestinationConnectorConfigInputTypedDict",
    "WeaviateDestinationConnectorConfigTypedDict",
    "WorkflowInformation",
    "WorkflowInformationTypedDict",
    "WorkflowJobType",
    "WorkflowNode",
    "WorkflowNodeTypedDict",
    "WorkflowSchedule",
    "WorkflowScheduleTypedDict",
    "WorkflowState",
    "WorkflowType",
    "ZendeskSourceConnectorConfig",
    "ZendeskSourceConnectorConfigInput",
    "ZendeskSourceConnectorConfigInputTypedDict",
    "ZendeskSourceConnectorConfigTypedDict",
]

_dynamic_imports: dict[str, str] = {
    "AstraDBConnectorConfig": ".astradbconnectorconfig",
    "AstraDBConnectorConfigTypedDict": ".astradbconnectorconfig",
    "AstraDBConnectorConfigInput": ".astradbconnectorconfiginput",
    "AstraDBConnectorConfigInputTypedDict": ".astradbconnectorconfiginput",
    "AzureAISearchConnectorConfig": ".azureaisearchconnectorconfig",
    "AzureAISearchConnectorConfigTypedDict": ".azureaisearchconnectorconfig",
    "AzureAISearchConnectorConfigInput": ".azureaisearchconnectorconfiginput",
    "AzureAISearchConnectorConfigInputTypedDict": ".azureaisearchconnectorconfiginput",
    "AzureSourceConnectorConfig": ".azuresourceconnectorconfig",
    "AzureSourceConnectorConfigTypedDict": ".azuresourceconnectorconfig",
    "AzureSourceConnectorConfigInput": ".azuresourceconnectorconfiginput",
    "AzureSourceConnectorConfigInputTypedDict": ".azuresourceconnectorconfiginput",
    "BodyRunWorkflow": ".body_run_workflow",
    "BodyRunWorkflowTypedDict": ".body_run_workflow",
    "InputFiles": ".body_run_workflow",
    "InputFilesTypedDict": ".body_run_workflow",
    "BoxSourceConnectorConfig": ".boxsourceconnectorconfig",
    "BoxSourceConnectorConfigTypedDict": ".boxsourceconnectorconfig",
    "BoxSourceConnectorConfigInput": ".boxsourceconnectorconfiginput",
    "BoxSourceConnectorConfigInputTypedDict": ".boxsourceconnectorconfiginput",
    "ConfluenceSourceConnectorConfig": ".confluencesourceconnectorconfig",
    "ConfluenceSourceConnectorConfigTypedDict": ".confluencesourceconnectorconfig",
    "ConfluenceSourceConnectorConfigInput": ".confluencesourceconnectorconfiginput",
    "ConfluenceSourceConnectorConfigInputTypedDict": ".confluencesourceconnectorconfiginput",
    "ConnectionCheckStatus": ".connectioncheckstatus",
    "CouchbaseDestinationConnectorConfig": ".couchbasedestinationconnectorconfig",
    "CouchbaseDestinationConnectorConfigTypedDict": ".couchbasedestinationconnectorconfig",
    "CouchbaseDestinationConnectorConfigInput": ".couchbasedestinationconnectorconfiginput",
    "CouchbaseDestinationConnectorConfigInputTypedDict": ".couchbasedestinationconnectorconfiginput",
    "CouchbaseSourceConnectorConfig": ".couchbasesourceconnectorconfig",
    "CouchbaseSourceConnectorConfigTypedDict": ".couchbasesourceconnectorconfig",
    "CouchbaseSourceConnectorConfigInput": ".couchbasesourceconnectorconfiginput",
    "CouchbaseSourceConnectorConfigInputTypedDict": ".couchbasesourceconnectorconfiginput",
    "Config": ".createdestinationconnector",
    "ConfigTypedDict": ".createdestinationconnector",
    "CreateDestinationConnector": ".createdestinationconnector",
    "CreateDestinationConnectorTypedDict": ".createdestinationconnector",
    "CreateSourceConnector": ".createsourceconnector",
    "CreateSourceConnectorConfig": ".createsourceconnector",
    "CreateSourceConnectorConfigTypedDict": ".createsourceconnector",
    "CreateSourceConnectorTypedDict": ".createsourceconnector",
    "CreateWorkflow": ".createworkflow",
    "CreateWorkflowTypedDict": ".createworkflow",
    "Schedule": ".createworkflow",
    "CronTabEntry": ".crontabentry",
    "CronTabEntryTypedDict": ".crontabentry",
    "DagNodeConnectionCheck": ".dagnodeconnectioncheck",
    "DagNodeConnectionCheckTypedDict": ".dagnodeconnectioncheck",
    "DatabricksVDTDestinationConnectorConfig": ".databricksvdtdestinationconnectorconfig",
    "DatabricksVDTDestinationConnectorConfigTypedDict": ".databricksvdtdestinationconnectorconfig",
    "DatabricksVDTDestinationConnectorConfigInput": ".databricksvdtdestinationconnectorconfiginput",
    "DatabricksVDTDestinationConnectorConfigInputTypedDict": ".databricksvdtdestinationconnectorconfiginput",
    "DatabricksVolumesConnectorConfig": ".databricksvolumesconnectorconfig",
    "DatabricksVolumesConnectorConfigTypedDict": ".databricksvolumesconnectorconfig",
    "DatabricksVolumesConnectorConfigInput": ".databricksvolumesconnectorconfiginput",
    "DatabricksVolumesConnectorConfigInputTypedDict": ".databricksvolumesconnectorconfiginput",
    "DeltaTableConnectorConfig": ".deltatableconnectorconfig",
    "DeltaTableConnectorConfigTypedDict": ".deltatableconnectorconfig",
    "DeltaTableConnectorConfigInput": ".deltatableconnectorconfiginput",
    "DeltaTableConnectorConfigInputTypedDict": ".deltatableconnectorconfiginput",
    "DestinationConnectorInformation": ".destinationconnectorinformation",
    "DestinationConnectorInformationConfig": ".destinationconnectorinformation",
    "DestinationConnectorInformationConfigTypedDict": ".destinationconnectorinformation",
    "DestinationConnectorInformationTypedDict": ".destinationconnectorinformation",
    "DestinationConnectorType": ".destinationconnectortype",
    "DropboxSourceConnectorConfig": ".dropboxsourceconnectorconfig",
    "DropboxSourceConnectorConfigTypedDict": ".dropboxsourceconnectorconfig",
    "DropboxSourceConnectorConfigInput": ".dropboxsourceconnectorconfiginput",
    "DropboxSourceConnectorConfigInputTypedDict": ".dropboxsourceconnectorconfiginput",
    "ElasticsearchConnectorConfig": ".elasticsearchconnectorconfig",
    "ElasticsearchConnectorConfigTypedDict": ".elasticsearchconnectorconfig",
    "ElasticsearchConnectorConfigInput": ".elasticsearchconnectorconfiginput",
    "ElasticsearchConnectorConfigInputTypedDict": ".elasticsearchconnectorconfiginput",
    "EncryptedSecret": ".encryptedsecret",
    "EncryptedSecretTypedDict": ".encryptedsecret",
    "EncryptionType": ".encryptiontype",
    "FailedFile": ".failedfile",
    "FailedFileTypedDict": ".failedfile",
    "GCSDestinationConnectorConfig": ".gcsdestinationconnectorconfig",
    "GCSDestinationConnectorConfigTypedDict": ".gcsdestinationconnectorconfig",
    "GCSDestinationConnectorConfigInput": ".gcsdestinationconnectorconfiginput",
    "GCSDestinationConnectorConfigInputTypedDict": ".gcsdestinationconnectorconfiginput",
    "GCSSourceConnectorConfig": ".gcssourceconnectorconfig",
    "GCSSourceConnectorConfigTypedDict": ".gcssourceconnectorconfig",
    "GCSSourceConnectorConfigInput": ".gcssourceconnectorconfiginput",
    "GCSSourceConnectorConfigInputTypedDict": ".gcssourceconnectorconfiginput",
    "GoogleDriveSourceConnectorConfig": ".googledrivesourceconnectorconfig",
    "GoogleDriveSourceConnectorConfigTypedDict": ".googledrivesourceconnectorconfig",
    "ServiceAccountKey": ".googledrivesourceconnectorconfig",
    "ServiceAccountKeyTypedDict": ".googledrivesourceconnectorconfig",
    "GoogleDriveSourceConnectorConfigInput": ".googledrivesourceconnectorconfiginput",
    "GoogleDriveSourceConnectorConfigInputServiceAccountKey": ".googledrivesourceconnectorconfiginput",
    "GoogleDriveSourceConnectorConfigInputServiceAccountKeyTypedDict": ".googledrivesourceconnectorconfiginput",
    "GoogleDriveSourceConnectorConfigInputTypedDict": ".googledrivesourceconnectorconfiginput",
    "IBMWatsonxS3DestinationConnectorConfig": ".ibmwatsonxs3destinationconnectorconfig",
    "IBMWatsonxS3DestinationConnectorConfigTypedDict": ".ibmwatsonxs3destinationconnectorconfig",
    "IBMWatsonxS3DestinationConnectorConfigInput": ".ibmwatsonxs3destinationconnectorconfiginput",
    "IBMWatsonxS3DestinationConnectorConfigInputTypedDict": ".ibmwatsonxs3destinationconnectorconfiginput",
    "JiraSourceConnectorConfig": ".jirasourceconnectorconfig",
    "JiraSourceConnectorConfigTypedDict": ".jirasourceconnectorconfig",
    "JiraSourceConnectorConfigInput": ".jirasourceconnectorconfiginput",
    "JiraSourceConnectorConfigInputTypedDict": ".jirasourceconnectorconfiginput",
    "JobDetails": ".jobdetails",
    "JobDetailsTypedDict": ".jobdetails",
    "JobFailedFiles": ".jobfailedfiles",
    "JobFailedFilesTypedDict": ".jobfailedfiles",
    "JobInformation": ".jobinformation",
    "JobInformationTypedDict": ".jobinformation",
    "JobNodeDetails": ".jobnodedetails",
    "JobNodeDetailsTypedDict": ".jobnodedetails",
    "JobProcessingStatus": ".jobprocessingstatus",
    "JobStatus": ".jobstatus",
    "KafkaCloudDestinationConnectorConfig": ".kafkaclouddestinationconnectorconfig",
    "KafkaCloudDestinationConnectorConfigTypedDict": ".kafkaclouddestinationconnectorconfig",
    "KafkaCloudDestinationConnectorConfigInput": ".kafkaclouddestinationconnectorconfiginput",
    "KafkaCloudDestinationConnectorConfigInputTypedDict": ".kafkaclouddestinationconnectorconfiginput",
    "KafkaCloudSourceConnectorConfig": ".kafkacloudsourceconnectorconfig",
    "KafkaCloudSourceConnectorConfigTypedDict": ".kafkacloudsourceconnectorconfig",
    "KafkaCloudSourceConnectorConfigInput": ".kafkacloudsourceconnectorconfiginput",
    "KafkaCloudSourceConnectorConfigInputTypedDict": ".kafkacloudsourceconnectorconfiginput",
    "MilvusDestinationConnectorConfig": ".milvusdestinationconnectorconfig",
    "MilvusDestinationConnectorConfigTypedDict": ".milvusdestinationconnectorconfig",
    "MilvusDestinationConnectorConfigInput": ".milvusdestinationconnectorconfiginput",
    "MilvusDestinationConnectorConfigInputTypedDict": ".milvusdestinationconnectorconfiginput",
    "MongoDBConnectorConfig": ".mongodbconnectorconfig",
    "MongoDBConnectorConfigTypedDict": ".mongodbconnectorconfig",
    "MongoDBConnectorConfigInput": ".mongodbconnectorconfiginput",
    "MongoDBConnectorConfigInputTypedDict": ".mongodbconnectorconfiginput",
    "Neo4jDestinationConnectorConfig": ".neo4jdestinationconnectorconfig",
    "Neo4jDestinationConnectorConfigTypedDict": ".neo4jdestinationconnectorconfig",
    "Neo4jDestinationConnectorConfigInput": ".neo4jdestinationconnectorconfiginput",
    "Neo4jDestinationConnectorConfigInputTypedDict": ".neo4jdestinationconnectorconfiginput",
    "NodeFileMetadata": ".nodefilemetadata",
    "NodeFileMetadataTypedDict": ".nodefilemetadata",
    "OneDriveDestinationConnectorConfig": ".onedrivedestinationconnectorconfig",
    "OneDriveDestinationConnectorConfigTypedDict": ".onedrivedestinationconnectorconfig",
    "OneDriveDestinationConnectorConfigInput": ".onedrivedestinationconnectorconfiginput",
    "OneDriveDestinationConnectorConfigInputTypedDict": ".onedrivedestinationconnectorconfiginput",
    "OneDriveSourceConnectorConfig": ".onedrivesourceconnectorconfig",
    "OneDriveSourceConnectorConfigTypedDict": ".onedrivesourceconnectorconfig",
    "OneDriveSourceConnectorConfigInput": ".onedrivesourceconnectorconfiginput",
    "OneDriveSourceConnectorConfigInputTypedDict": ".onedrivesourceconnectorconfiginput",
    "OutlookSourceConnectorConfig": ".outlooksourceconnectorconfig",
    "OutlookSourceConnectorConfigTypedDict": ".outlooksourceconnectorconfig",
    "OutlookSourceConnectorConfigInput": ".outlooksourceconnectorconfiginput",
    "OutlookSourceConnectorConfigInputTypedDict": ".outlooksourceconnectorconfiginput",
    "Files": ".partition_parameters",
    "FilesTypedDict": ".partition_parameters",
    "OutputFormat": ".partition_parameters",
    "PartitionParameters": ".partition_parameters",
    "PartitionParametersTypedDict": ".partition_parameters",
    "Strategy": ".partition_parameters",
    "VLMModel": ".partition_parameters",
    "VLMModelProvider": ".partition_parameters",
    "PemAuthResponse": ".pemauthresponse",
    "PemAuthResponseTypedDict": ".pemauthresponse",
    "PineconeDestinationConnectorConfig": ".pineconedestinationconnectorconfig",
    "PineconeDestinationConnectorConfigTypedDict": ".pineconedestinationconnectorconfig",
    "PineconeDestinationConnectorConfigInput": ".pineconedestinationconnectorconfiginput",
    "PineconeDestinationConnectorConfigInputTypedDict": ".pineconedestinationconnectorconfiginput",
    "PostgresDestinationConnectorConfig": ".postgresdestinationconnectorconfig",
    "PostgresDestinationConnectorConfigTypedDict": ".postgresdestinationconnectorconfig",
    "PostgresDestinationConnectorConfigInput": ".postgresdestinationconnectorconfiginput",
    "PostgresDestinationConnectorConfigInputTypedDict": ".postgresdestinationconnectorconfiginput",
    "PostgresSourceConnectorConfig": ".postgressourceconnectorconfig",
    "PostgresSourceConnectorConfigTypedDict": ".postgressourceconnectorconfig",
    "PostgresSourceConnectorConfigInput": ".postgressourceconnectorconfiginput",
    "PostgresSourceConnectorConfigInputTypedDict": ".postgressourceconnectorconfiginput",
    "QdrantCloudDestinationConnectorConfig": ".qdrantclouddestinationconnectorconfig",
    "QdrantCloudDestinationConnectorConfigTypedDict": ".qdrantclouddestinationconnectorconfig",
    "QdrantCloudDestinationConnectorConfigInput": ".qdrantclouddestinationconnectorconfiginput",
    "QdrantCloudDestinationConnectorConfigInputTypedDict": ".qdrantclouddestinationconnectorconfiginput",
    "RedisDestinationConnectorConfig": ".redisdestinationconnectorconfig",
    "RedisDestinationConnectorConfigTypedDict": ".redisdestinationconnectorconfig",
    "RedisDestinationConnectorConfigInput": ".redisdestinationconnectorconfiginput",
    "RedisDestinationConnectorConfigInputTypedDict": ".redisdestinationconnectorconfiginput",
    "S3DestinationConnectorConfig": ".s3destinationconnectorconfig",
    "S3DestinationConnectorConfigTypedDict": ".s3destinationconnectorconfig",
    "S3DestinationConnectorConfigInput": ".s3destinationconnectorconfiginput",
    "S3DestinationConnectorConfigInputTypedDict": ".s3destinationconnectorconfiginput",
    "S3SourceConnectorConfig": ".s3sourceconnectorconfig",
    "S3SourceConnectorConfigTypedDict": ".s3sourceconnectorconfig",
    "S3SourceConnectorConfigInput": ".s3sourceconnectorconfiginput",
    "S3SourceConnectorConfigInputTypedDict": ".s3sourceconnectorconfiginput",
    "SalesforceSourceConnectorConfig": ".salesforcesourceconnectorconfig",
    "SalesforceSourceConnectorConfigTypedDict": ".salesforcesourceconnectorconfig",
    "SalesforceSourceConnectorConfigInput": ".salesforcesourceconnectorconfiginput",
    "SalesforceSourceConnectorConfigInputTypedDict": ".salesforcesourceconnectorconfiginput",
    "SecretReference": ".secretreference",
    "SecretReferenceTypedDict": ".secretreference",
    "Security": ".security",
    "SecurityTypedDict": ".security",
    "SharePointSourceConnectorConfig": ".sharepointsourceconnectorconfig",
    "SharePointSourceConnectorConfigTypedDict": ".sharepointsourceconnectorconfig",
    "SharePointSourceConnectorConfigInput": ".sharepointsourceconnectorconfiginput",
    "SharePointSourceConnectorConfigInputTypedDict": ".sharepointsourceconnectorconfiginput",
    "SnowflakeDestinationConnectorConfig": ".snowflakedestinationconnectorconfig",
    "SnowflakeDestinationConnectorConfigTypedDict": ".snowflakedestinationconnectorconfig",
    "SnowflakeDestinationConnectorConfigInput": ".snowflakedestinationconnectorconfiginput",
    "SnowflakeDestinationConnectorConfigInputTypedDict": ".snowflakedestinationconnectorconfiginput",
    "SnowflakeSourceConnectorConfig": ".snowflakesourceconnectorconfig",
    "SnowflakeSourceConnectorConfigTypedDict": ".snowflakesourceconnectorconfig",
    "SnowflakeSourceConnectorConfigInput": ".snowflakesourceconnectorconfiginput",
    "SnowflakeSourceConnectorConfigInputTypedDict": ".snowflakesourceconnectorconfiginput",
    "SortDirection": ".sortdirection",
    "SourceConnectorInformation": ".sourceconnectorinformation",
    "SourceConnectorInformationConfig": ".sourceconnectorinformation",
    "SourceConnectorInformationConfigTypedDict": ".sourceconnectorinformation",
    "SourceConnectorInformationTypedDict": ".sourceconnectorinformation",
    "SourceConnectorType": ".sourceconnectortype",
    "UpdateDestinationConnector": ".updatedestinationconnector",
    "UpdateDestinationConnectorConfig": ".updatedestinationconnector",
    "UpdateDestinationConnectorConfigTypedDict": ".updatedestinationconnector",
    "UpdateDestinationConnectorTypedDict": ".updatedestinationconnector",
    "UpdateSourceConnector": ".updatesourceconnector",
    "UpdateSourceConnectorConfig": ".updatesourceconnector",
    "UpdateSourceConnectorConfigTypedDict": ".updatesourceconnector",
    "UpdateSourceConnectorTypedDict": ".updatesourceconnector",
    "UpdateWorkflow": ".updateworkflow",
    "UpdateWorkflowSchedule": ".updateworkflow",
    "UpdateWorkflowTypedDict": ".updateworkflow",
    "Loc": ".validationerror",
    "LocTypedDict": ".validationerror",
    "ValidationError": ".validationerror",
    "ValidationErrorTypedDict": ".validationerror",
    "WeaviateDestinationConnectorConfig": ".weaviatedestinationconnectorconfig",
    "WeaviateDestinationConnectorConfigTypedDict": ".weaviatedestinationconnectorconfig",
    "WeaviateDestinationConnectorConfigInput": ".weaviatedestinationconnectorconfiginput",
    "WeaviateDestinationConnectorConfigInputTypedDict": ".weaviatedestinationconnectorconfiginput",
    "WorkflowInformation": ".workflowinformation",
    "WorkflowInformationTypedDict": ".workflowinformation",
    "WorkflowJobType": ".workflowjobtype",
    "WorkflowNode": ".workflownode",
    "WorkflowNodeTypedDict": ".workflownode",
    "WorkflowSchedule": ".workflowschedule",
    "WorkflowScheduleTypedDict": ".workflowschedule",
    "WorkflowState": ".workflowstate",
    "WorkflowType": ".workflowtype",
    "ZendeskSourceConnectorConfig": ".zendesksourceconnectorconfig",
    "ZendeskSourceConnectorConfigTypedDict": ".zendesksourceconnectorconfig",
    "ZendeskSourceConnectorConfigInput": ".zendesksourceconnectorconfiginput",
    "ZendeskSourceConnectorConfigInputTypedDict": ".zendesksourceconnectorconfiginput",
}


def __getattr__(attr_name: str) -> object:
    module_name = _dynamic_imports.get(attr_name)
    if module_name is None:
        raise AttributeError(
            f"No {attr_name} found in _dynamic_imports for module name -> {__name__} "
        )

    try:
        module = import_module(module_name, __package__)
        result = getattr(module, attr_name)
        return result
    except ImportError as e:
        raise ImportError(
            f"Failed to import {attr_name} from {module_name}: {e}"
        ) from e
    except AttributeError as e:
        raise AttributeError(
            f"Failed to get {attr_name} from {module_name}: {e}"
        ) from e


def __dir__():
    lazy_attrs = list(_dynamic_imports.keys())
    return sorted(lazy_attrs)
