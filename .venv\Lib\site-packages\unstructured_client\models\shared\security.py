"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.types import BaseModel
from unstructured_client.utils import FieldMetadata, SecurityMetadata


class SecurityTypedDict(TypedDict):
    api_key_auth: NotRequired[str]


class Security(BaseModel):
    api_key_auth: Annotated[
        Optional[str],
        FieldMetadata(
            security=SecurityMetadata(
                scheme=True,
                scheme_type="apiKey",
                sub_type="header",
                field_name="unstructured-api-key",
            )
        ),
    ] = None
