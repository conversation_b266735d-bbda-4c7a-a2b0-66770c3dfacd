"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class Neo4jDestinationConnectorConfigTypedDict(TypedDict):
    batch_size: int
    database: str
    password: str
    uri: str
    username: str


class Neo4jDestinationConnectorConfig(BaseModel):
    batch_size: int

    database: str

    password: str

    uri: str

    username: str
