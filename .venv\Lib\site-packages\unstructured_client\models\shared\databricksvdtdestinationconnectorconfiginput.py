"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
import pydantic
from pydantic import model_serializer
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class DatabricksVDTDestinationConnectorConfigInputTypedDict(TypedDict):
    catalog: str
    http_path: str
    server_hostname: str
    volume: str
    client_id: NotRequired[Nullable[str]]
    client_secret: NotRequired[Nullable[str]]
    database: NotRequired[str]
    schema_: NotRequired[str]
    table_name: NotRequired[Nullable[str]]
    token: NotRequired[Nullable[str]]
    volume_path: NotRequired[Nullable[str]]


class DatabricksVDTDestinationConnectorConfigInput(BaseModel):
    catalog: str

    http_path: str

    server_hostname: str

    volume: str

    client_id: OptionalNullable[str] = UNSET

    client_secret: OptionalNullable[str] = UNSET

    database: Optional[str] = "default"

    schema_: Annotated[Optional[str], pydantic.Field(alias="schema")] = None

    table_name: OptionalNullable[str] = UNSET

    token: OptionalNullable[str] = UNSET

    volume_path: OptionalNullable[str] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "client_id",
            "client_secret",
            "database",
            "schema",
            "table_name",
            "token",
            "volume_path",
        ]
        nullable_fields = [
            "client_id",
            "client_secret",
            "table_name",
            "token",
            "volume_path",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
