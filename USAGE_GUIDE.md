# PDF Information Extractor - Usage Guide

This guide provides step-by-step instructions for using the PDF information extractor scripts.

## Quick Start

### 1. Test Your Setup

First, test if everything is working:

```bash
python test_extractor.py
```

This will check if all required libraries are installed and test basic PDF extraction.

### 2. Simple Extraction (No LLM Required)

For basic text extraction and keyword search:

```bash
# Extract text and search for keywords
python simple_pdf_extractor.py "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf"

# Save results to file
python simple_pdf_extractor.py "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf" -o simple_results.json
```

### 3. AI-Powered Extraction (Requires LLM)

For intelligent information extraction using Llama 3.2:

```bash
# Using Ollama (recommended)
python pdf_extractor_llama.py "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf"

# Using Transformers
python pdf_extractor_llama.py "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf" --use-transformers

# Save results to file
python pdf_extractor_llama.py "FW_ Project _ PPAP Adequacy & Accuracy (Strictly Confidential)/01. PSW - Part Submission Warrant.pdf" -o ai_results.json
```

## Installation Options

### Option A: Automatic Setup

```bash
python setup_llama_extractor.py
```

### Option B: Manual Installation

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **For Ollama (Recommended):**
```bash
# Install Ollama from https://ollama.ai/
# Then pull the model:
ollama pull llama3.2
```

3. **For Transformers (Alternative):**
```bash
# No additional setup needed - model downloads automatically
```

## Script Comparison

| Feature | simple_pdf_extractor.py | pdf_extractor_llama.py |
|---------|------------------------|------------------------|
| **Dependencies** | Only unstructured | unstructured + LLM |
| **Extraction Method** | Keyword search | AI-powered analysis |
| **Accuracy** | Basic | High |
| **Setup Complexity** | Simple | Moderate |
| **Processing Speed** | Fast | Slower |
| **Best For** | Quick testing, basic extraction | Production use, accurate extraction |

## Expected Output

### Simple Extractor Output

```json
{
  "pdf_path": "document.pdf",
  "extraction_method": "unstructured_simple",
  "text_extraction": {
    "total_elements": 45,
    "text_length": 3420,
    "element_types": ["Title", "NarrativeText", "Table"]
  },
  "found_information": {
    "Part Number": ["Part Number: P80009246A"],
    "Date": ["Date: 2024-01-15"],
    "Weight": null
  }
}
```

### AI Extractor Output

```json
{
  "pdf_path": "document.pdf",
  "extraction_method": "ollama",
  "extracted_information": {
    "Part Number": "P80009246A",
    "Customer Part Number": "ABC123",
    "Date": "2024-01-15",
    "Weight": "2.5 kg",
    "Purchase Order No.": "PO123456"
  }
}
```

## Troubleshooting

### Common Issues

1. **"unstructured not found"**
   ```bash
   pip install unstructured[all-docs]
   ```

2. **"Ollama not available"**
   - Install Ollama from https://ollama.ai/
   - Run: `ollama pull llama3.2`
   - Make sure Ollama service is running

3. **"PDF extraction failed"**
   - Check if PDF file exists
   - Ensure PDF is not password protected
   - Try with a different PDF file

4. **"Out of memory" (with transformers)**
   - Use Ollama instead: remove `--use-transformers` flag
   - Close other applications to free memory

### Performance Tips

1. **For faster processing:** Use `simple_pdf_extractor.py` for basic needs
2. **For better accuracy:** Use `pdf_extractor_llama.py` with Ollama
3. **For batch processing:** Process files one at a time

## File Structure

After running the scripts, you'll have:

```
your_project/
├── pdf_extractor_llama.py          # Main AI-powered extractor
├── simple_pdf_extractor.py         # Simple keyword-based extractor
├── test_extractor.py               # Test script
├── setup_llama_extractor.py        # Setup script
├── requirements.txt                # Python dependencies
├── README_LLAMA_EXTRACTOR.md       # Detailed documentation
├── USAGE_GUIDE.md                  # This file
└── results/                        # Output files (created when saving results)
    ├── simple_results.json
    └── ai_results.json
```

## Next Steps

1. **Test with your PDF:** Replace the sample PDF path with your actual file
2. **Customize categories:** Edit the `categories` list in the scripts for your specific needs
3. **Batch processing:** Create a loop to process multiple files
4. **Integration:** Import the classes into your own Python scripts

## Support

If you encounter issues:

1. Run `python test_extractor.py` to diagnose problems
2. Check the error messages for specific guidance
3. Ensure all dependencies are properly installed
4. Try the simple extractor first before using the AI version
