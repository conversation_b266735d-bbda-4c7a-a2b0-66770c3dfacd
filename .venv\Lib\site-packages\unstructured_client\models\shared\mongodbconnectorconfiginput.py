"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class MongoDBConnectorConfigInputTypedDict(TypedDict):
    collection: str
    database: str
    uri: str


class MongoDBConnectorConfigInput(BaseModel):
    collection: str

    database: str

    uri: str
