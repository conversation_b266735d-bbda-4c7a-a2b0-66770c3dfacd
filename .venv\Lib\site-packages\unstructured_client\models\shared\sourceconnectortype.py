"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from enum import Enum


class SourceConnectorType(str, Enum):
    AZURE = "azure"
    BOX = "box"
    CONFLUENCE = "confluence"
    COUCHBASE = "couchbase"
    DATABRICKS_VOLUMES = "databricks_volumes"
    DROPBOX = "dropbox"
    ELASTICSEARCH = "elasticsearch"
    GCS = "gcs"
    GOOGLE_DRIVE = "google_drive"
    KAFKA_CLOUD = "kafka-cloud"
    MONGODB = "mongodb"
    ONEDRIVE = "onedrive"
    OUTLOOK = "outlook"
    POSTGRES = "postgres"
    S3 = "s3"
    SALESFORCE = "salesforce"
    SHAREPOINT = "sharepoint"
    SLACK = "slack"
    SNOWFLAKE = "snowflake"
    JIRA = "jira"
    ZENDESK = "zendesk"
