"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .secretreference import SecretReference, SecretReferenceTypedDict
from pydantic import model_serializer
from typing import List, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


ServiceAccountKeyTypedDict = TypeAliasType(
    "ServiceAccountKeyTypedDict", Union[SecretReferenceTypedDict, str]
)


ServiceAccountKey = TypeAliasType("ServiceAccountKey", Union[SecretReference, str])


class GoogleDriveSourceConnectorConfigTypedDict(TypedDict):
    drive_id: str
    recursive: bool
    service_account_key: ServiceAccountKeyTypedDict
    extensions: NotRequired[Nullable[List[str]]]


class GoogleDriveSourceConnectorConfig(BaseModel):
    drive_id: str

    recursive: bool

    service_account_key: ServiceAccountKey

    extensions: OptionalNullable[List[str]] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["extensions"]
        nullable_fields = ["extensions"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
