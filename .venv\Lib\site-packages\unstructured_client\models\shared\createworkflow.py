"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .workflownode import WorkflowNode, WorkflowNodeTypedDict
from .workflowtype import WorkflowType
from enum import Enum
from pydantic import model_serializer
from typing import List
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class Schedule(str, Enum):
    EVERY_15_MINUTES = "every 15 minutes"
    EVERY_HOUR = "every hour"
    EVERY_2_HOURS = "every 2 hours"
    EVERY_4_HOURS = "every 4 hours"
    EVERY_6_HOURS = "every 6 hours"
    EVERY_8_HOURS = "every 8 hours"
    EVERY_10_HOURS = "every 10 hours"
    EVERY_12_HOURS = "every 12 hours"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"


class CreateWorkflowTypedDict(TypedDict):
    name: str
    workflow_type: WorkflowType
    destination_id: NotRequired[Nullable[str]]
    reprocess_all: NotRequired[Nullable[bool]]
    schedule: NotRequired[Nullable[Schedule]]
    source_id: NotRequired[Nullable[str]]
    workflow_nodes: NotRequired[Nullable[List[WorkflowNodeTypedDict]]]


class CreateWorkflow(BaseModel):
    name: str

    workflow_type: WorkflowType

    destination_id: OptionalNullable[str] = UNSET

    reprocess_all: OptionalNullable[bool] = UNSET

    schedule: OptionalNullable[Schedule] = UNSET

    source_id: OptionalNullable[str] = UNSET

    workflow_nodes: OptionalNullable[List[WorkflowNode]] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "destination_id",
            "reprocess_all",
            "schedule",
            "source_id",
            "workflow_nodes",
        ]
        nullable_fields = [
            "destination_id",
            "reprocess_all",
            "schedule",
            "source_id",
            "workflow_nodes",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
