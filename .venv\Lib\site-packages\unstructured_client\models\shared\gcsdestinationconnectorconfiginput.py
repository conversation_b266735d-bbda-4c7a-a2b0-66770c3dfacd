"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class GCSDestinationConnectorConfigInputTypedDict(TypedDict):
    remote_url: str
    service_account_key: str


class GCSDestinationConnectorConfigInput(BaseModel):
    remote_url: str

    service_account_key: str
