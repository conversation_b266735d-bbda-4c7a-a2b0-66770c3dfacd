test_unstructured/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/__pycache__/test_utils.cpython-310.pyc,,
test_unstructured/__pycache__/unit_utils.cpython-310.pyc,,
test_unstructured/chunking/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/chunking/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/chunking/__pycache__/test_base.cpython-310.pyc,,
test_unstructured/chunking/__pycache__/test_basic.cpython-310.pyc,,
test_unstructured/chunking/__pycache__/test_dispatch.cpython-310.pyc,,
test_unstructured/chunking/__pycache__/test_html_output.cpython-310.pyc,,
test_unstructured/chunking/__pycache__/test_title.cpython-310.pyc,,
test_unstructured/chunking/test_base.py,sha256=cLCbSX2Dr-ckG774tAaxurZ0peiV0xfbRn7IeISsyis,74047
test_unstructured/chunking/test_basic.py,sha256=x1l8Rnl4tnG1_VjwdJn-LTCZbGXGPyzF3pXGNZuntfQ,8309
test_unstructured/chunking/test_dispatch.py,sha256=xHD5BTim8aTLmi7PH65mKvXmrJslAf6xYd-sKgd1fSo,3255
test_unstructured/chunking/test_html_output.py,sha256=uJ7jdvuZYTstgE5xrQ-BF1QfFvowJbOJjwO66LAjRu8,3253
test_unstructured/chunking/test_title.py,sha256=o4OHl_KoFnGznLJ1cssB5C1YcBnDsFoFVpM3h4wWM9A,19446
test_unstructured/cleaners/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/cleaners/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/cleaners/__pycache__/test_core.cpython-310.pyc,,
test_unstructured/cleaners/__pycache__/test_extract.cpython-310.pyc,,
test_unstructured/cleaners/__pycache__/test_translate.cpython-310.pyc,,
test_unstructured/cleaners/test_core.py,sha256=3FVidZQD-qK9lCsiKftkARghqCAYcelHbBD0BnLve8k,10357
test_unstructured/cleaners/test_extract.py,sha256=A5g85ipVESZkD_4TA0s7WOujddlA3sqsHnSFQlzMrus,4679
test_unstructured/cleaners/test_translate.py,sha256=TWRfa05gpqffjIShfkU2wEG24wBYfg4TOjaJJP_04qA,2074
test_unstructured/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/common/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/common/__pycache__/test_html_table.cpython-310.pyc,,
test_unstructured/common/test_html_table.py,sha256=fUcK9yFK_ArNBeDrfxKft8GAfsU9bqp3WEEdxdKTLPM,7579
test_unstructured/documents/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/documents/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/documents/__pycache__/test_coordinates.cpython-310.pyc,,
test_unstructured/documents/__pycache__/test_elements.cpython-310.pyc,,
test_unstructured/documents/__pycache__/test_mappings.cpython-310.pyc,,
test_unstructured/documents/__pycache__/test_ontology_to_unstructured_parsing.cpython-310.pyc,,
test_unstructured/documents/test_coordinates.py,sha256=8kIj45xu8SSf6vt3LRxoraMpY2yjiVcPsXYFPkpz2hU,2795
test_unstructured/documents/test_elements.py,sha256=4fkmdTjaLrAtzuEKFQwrV53dAcfKrASJjbS_yoQYDMQ,28660
test_unstructured/documents/test_mappings.py,sha256=9-LYAJQuJ7jEALqEJoBu3Jso80oVarsvQG4XoRiZZD4,1877
test_unstructured/documents/test_ontology_to_unstructured_parsing.py,sha256=pcGb5cE6e71uNQbGEsum3WpsUdEsg86Eh9rr15uKWB0,12007
test_unstructured/embed/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/embed/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/embed/__pycache__/test_mixedbreadai.cpython-310.pyc,,
test_unstructured/embed/__pycache__/test_octoai.cpython-310.pyc,,
test_unstructured/embed/__pycache__/test_openai.cpython-310.pyc,,
test_unstructured/embed/__pycache__/test_vertexai.cpython-310.pyc,,
test_unstructured/embed/__pycache__/test_voyageai.cpython-310.pyc,,
test_unstructured/embed/test_mixedbreadai.py,sha256=3XzkygDeKXUAWiVEXKSdBi5ue8ilXqxZs9WxSgx9kek,1357
test_unstructured/embed/test_octoai.py,sha256=ok4ZO_80zuQpI16mASLTlregGCiFwpJOWnsafNn4U80,861
test_unstructured/embed/test_openai.py,sha256=1vsK1DuJu1krdes1uOBpzdFlnrHc5WbQ8CzzsJFh6H0,861
test_unstructured/embed/test_vertexai.py,sha256=uZ5aCGZgJjlx_SD1jKozukt7dWXm_hNXyiUuK2gPku0,876
test_unstructured/embed/test_voyageai.py,sha256=TzvhVQcWj9j1okhiOGci3LcelSDfdw8rIdLCN2w6U5c,1017
test_unstructured/file_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/file_utils/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/file_utils/__pycache__/test_file_conversion.cpython-310.pyc,,
test_unstructured/file_utils/__pycache__/test_filetype.cpython-310.pyc,,
test_unstructured/file_utils/__pycache__/test_model.cpython-310.pyc,,
test_unstructured/file_utils/test_file_conversion.py,sha256=dtZ0Q4uMk46rbO25ofVQrDKwJCi8N8QmMH6jJidXSL4,1942
test_unstructured/file_utils/test_filetype.py,sha256=LhGxuAP3gG9dkiaIZ98pYvCrQ_NeDJ1uodld-pRUNlE,40400
test_unstructured/file_utils/test_model.py,sha256=wfE8wD04wyKz2bDqtqMdNVmBL0TQOSAmsNWLHAq5SSY,8957
test_unstructured/metrics/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/metrics/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_element_type.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_evaluate.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_table_alignment.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_table_detection_metrics.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_table_formats.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_table_structure.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_text_extraction.cpython-310.pyc,,
test_unstructured/metrics/__pycache__/test_utils.cpython-310.pyc,,
test_unstructured/metrics/test_element_type.py,sha256=Yd7Tzxw29nlCqCjzL7LvRh7m9AfWuE9PO8I3u93pD8s,3388
test_unstructured/metrics/test_evaluate.py,sha256=_oDyY-itd_fGFcNSI1QJwzD0v8GAZri3ujYYJAcrdi4,24607
test_unstructured/metrics/test_table_alignment.py,sha256=li4P_NLr5OaWCDR2adGadi_iycc_uzi0U5W5SbwVCAA,554
test_unstructured/metrics/test_table_detection_metrics.py,sha256=j4F9UdrRuSqp144P-Bxt03sANjQ3ovlalEIc8QYzopc,1555
test_unstructured/metrics/test_table_formats.py,sha256=esS-Ri8FQ9_nRCs0HVHrR2bkYevT3H_zML0p_BbmLn8,1357
test_unstructured/metrics/test_table_structure.py,sha256=9iMPU-HJ3ZwTZ2e7MljVkI0HAHqLKCFFNmkef1E4VKA,19704
test_unstructured/metrics/test_text_extraction.py,sha256=baLWgeXw4mwxCYM9aKbDEcu13foKL83M82_gILGSVM0,27042
test_unstructured/metrics/test_utils.py,sha256=PxMmFRjoHbJ2W-8YDBjJamLQb5a9n8JtQM6nsSmWdgU,925
test_unstructured/nlp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/nlp/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/nlp/__pycache__/mock_nltk.cpython-310.pyc,,
test_unstructured/nlp/__pycache__/test_partition.cpython-310.pyc,,
test_unstructured/nlp/__pycache__/test_tokenize.cpython-310.pyc,,
test_unstructured/nlp/mock_nltk.py,sha256=PsoZesQcrTP4Gxkx6_1CAI8TuYgVrLF2bDPP-i_nR6A,566
test_unstructured/nlp/test_partition.py,sha256=qz883Zaw3nFKv2fDVMng2TsY9FxY7ujvkYw3_RDTsUM,15
test_unstructured/nlp/test_tokenize.py,sha256=AS_MOlSI3H0oFJAV_X1pbT1UYVzIwxGhH_hAuYpZFZQ,2110
test_unstructured/partition/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/partition/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/partition/__pycache__/conftest.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_api.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_auto.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_constants.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_csv.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_doc.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_docx.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_email.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_epub.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_json.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_md.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_msg.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_ndjson.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_odt.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_org.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_ppt.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_pptx.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_rst.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_rtf.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_strategies.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_text.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_text_type.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_tsv.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_xlsx.cpython-310.pyc,,
test_unstructured/partition/__pycache__/test_xml.cpython-310.pyc,,
test_unstructured/partition/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/partition/common/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/partition/common/__pycache__/test_common.cpython-310.pyc,,
test_unstructured/partition/common/__pycache__/test_lang.cpython-310.pyc,,
test_unstructured/partition/common/__pycache__/test_metadata.cpython-310.pyc,,
test_unstructured/partition/common/test_common.py,sha256=mppvlXqFeTJX_usE26ozaGOINjZL54TEFquOEn3fJo0,13653
test_unstructured/partition/common/test_lang.py,sha256=Mw_GPafrlQRo-64vkZSj7t1l7fi5dhBeV0zx43WjZxY,8981
test_unstructured/partition/common/test_metadata.py,sha256=uCykd6sldq7xrEd0jtqj7tNjEu6I8s3EohvzqkbHJto,20449
test_unstructured/partition/conftest.py,sha256=eYZeMvCxxTqmNYghD5XewLToWKNHAmWLYKGj4uiMjt0,652
test_unstructured/partition/html/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/partition/html/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_convert.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_html_to_ontology_parsing.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_html_to_unstructured_and_back_parsing.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_html_utils.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_parser.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_partition.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_partition_v2.cpython-310.pyc,,
test_unstructured/partition/html/__pycache__/test_unstructured_elements_to_ontology_parsing.cpython-310.pyc,,
test_unstructured/partition/html/test_convert.py,sha256=bN9cc6MqrJgpWuBToAmUMc8uOEBZvp-QkYOa2Y7PFm0,18123
test_unstructured/partition/html/test_html_to_ontology_parsing.py,sha256=O-da-vxYHt7HQ_PgFHwDsHWCgabxs5tv6kmWzSil-zY,18844
test_unstructured/partition/html/test_html_to_unstructured_and_back_parsing.py,sha256=g2D7ngX24a2OZnfvxDVQZ5RdYACo4wiaxE_xMtlg6UU,15339
test_unstructured/partition/html/test_html_utils.py,sha256=ltlllMtcDlY4QFhhttue_eZgOafFYuIayYJ8gc_0nac,1069
test_unstructured/partition/html/test_parser.py,sha256=M99jj7Nw-EWDxwQ5aAz-Fwf4x3lnRdwYHZPIFwgwkTc,56536
test_unstructured/partition/html/test_partition.py,sha256=fRM4HIYIL-OsJyESpp_fnWn_j83Gwd9qY9M__5VMe4A,52492
test_unstructured/partition/html/test_partition_v2.py,sha256=Ue6FPeiIGrcCerVHQ0k6-UfMjuPg7r5rjajrcSsZeoA,2064
test_unstructured/partition/html/test_unstructured_elements_to_ontology_parsing.py,sha256=MvMxu-T9JfW-oZqLFUYbUMzoTNQiCd0Mx33Os65b81A,4903
test_unstructured/partition/pdf_image/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/partition/pdf_image/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/conftest.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_analysis.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_image.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_inference_utils.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_ocr.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_pdf.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_pdf_image_utils.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_pdfminer_processing.cpython-310.pyc,,
test_unstructured/partition/pdf_image/__pycache__/test_pdfminer_utils.cpython-310.pyc,,
test_unstructured/partition/pdf_image/conftest.py,sha256=ejimRq_95Bb3Hfm6Yjyzu5tudx7ieidQ1gFXSUEpSMI,2293
test_unstructured/partition/pdf_image/test_analysis.py,sha256=T-NZKcfZLYM-pTZfUeWlYHfXTKjnv7b3_v5Fnslg3V0,5147
test_unstructured/partition/pdf_image/test_image.py,sha256=b2tLY240c2Z8r2ykl8K9tLQgAr1lTFIhCcLFmpmRpcY,23368
test_unstructured/partition/pdf_image/test_inference_utils.py,sha256=gDtk6QhNQAb6RWMglq-E1Nc7bLNr0T_Ia0icSY4mnmA,5924
test_unstructured/partition/pdf_image/test_ocr.py,sha256=iRFjByiypkCaFffli-23HmqzsltrLQPL7afx3A-MezQ,21539
test_unstructured/partition/pdf_image/test_pdf.py,sha256=_YWIChOGkQ96qYGhthBI_7n8-DqfPVQhcFswZ8y9r04,56669
test_unstructured/partition/pdf_image/test_pdf_image_utils.py,sha256=2fUBrqc2QYmFR_-zP2NGnAvmKIAti9Ou_QCNayXY3Qg,12826
test_unstructured/partition/pdf_image/test_pdfminer_processing.py,sha256=nZ3zD5p5tnvUwMHdcW7Zw5Ey61yehBPjPJhXpcyIBFg,9141
test_unstructured/partition/pdf_image/test_pdfminer_utils.py,sha256=vkedoo8pYVaEJrj_TKJHxDbyfUVkkFPfoiR4HjQJnt8,1104
test_unstructured/partition/test_api.py,sha256=84qSQVpamNLEyMsNpvaPVRpDLx18po6X02l7xipoems,23942
test_unstructured/partition/test_auto.py,sha256=pSBu4nFYbzJR-RGg9FwKO_U3kinmPcNNgei_nPUKz9I,50486
test_unstructured/partition/test_constants.py,sha256=3T8UDe-gwJb2yEtyD43bVuOFpby8uqpPdXyBozOg0OU,3515
test_unstructured/partition/test_csv.py,sha256=1FvujpyyJoN118olOdJNcUu7-DBoqXp-DMPS3efT8w0,12319
test_unstructured/partition/test_doc.py,sha256=SrrvLG77eR210TtOPX0u-2lppvkFBDs0RNAAP57_rck,9934
test_unstructured/partition/test_docx.py,sha256=0pHy4D-U4hJ1swk23P6FVE7VyBHGX4QDUyqCfD5gqx4,48962
test_unstructured/partition/test_email.py,sha256=JB7XSvpnEig4V3kKsNk7A0ZOVHVDUsXCGFJS923Xojs,24716
test_unstructured/partition/test_epub.py,sha256=uAummMB0pz-OwR6QbCGBl66HbDCH9oAmyomzhQuLL0w,6505
test_unstructured/partition/test_json.py,sha256=Lk6ghCXif4sYoeClc8D61rMbMT4EgLdpmASthwb1eTc,11288
test_unstructured/partition/test_md.py,sha256=ULIKpVVI58ZBZNpKRKnBmS1FDeLSWv9s9oEZiAceA78,10507
test_unstructured/partition/test_msg.py,sha256=-T1EcsM-EKUlAxzL3k_Z9B0931fwDJtmXCpToqkNGs0,16980
test_unstructured/partition/test_ndjson.py,sha256=hMr6FnjVI1fu_a6WeBF83tw66faZ6X9IJaij-E6M-aQ,11163
test_unstructured/partition/test_odt.py,sha256=oHMon5QMPRO2oSvz9OtfRlQl3zmi4Ab14GfjteoZ2DU,7751
test_unstructured/partition/test_org.py,sha256=yhe76gkM3xBsd97AoV-NOECAl2Si0Wk0obVa-G6mDBI,5765
test_unstructured/partition/test_ppt.py,sha256=AszuiZi3HESwZaE1PTGYgEQd5DUi4VI_KbHJqvV3Ig4,6917
test_unstructured/partition/test_pptx.py,sha256=iHIcUmbu-gSFBOVFAFOTYQyUiQJWtAsJ8YSgdGSh800,30211
test_unstructured/partition/test_rst.py,sha256=fg1YcPeaZkgl2v7z1qq729yvK4RJjccfc24vP9gy9ec,5122
test_unstructured/partition/test_rtf.py,sha256=f9Nw6Tj7lnzF0DB3H7E6l0njOgrO9uGjpCO3zYmsy9Y,4591
test_unstructured/partition/test_strategies.py,sha256=uNmDKQwOWgXGqKh0kX63XI_444CuC7nowAWOiv0j7Sw,4344
test_unstructured/partition/test_text.py,sha256=f_VODf0KXD_KcPzfOdzEFyLVBzMm61YpU0YqCmq4gbY,14834
test_unstructured/partition/test_text_type.py,sha256=Q_cdaDlzlEgw2FZ0dm9LSGQOJohFPR7_U4DD9K3Xv5A,12603
test_unstructured/partition/test_tsv.py,sha256=h_3WZ9IpqsAPMRPGKQCaPnL7q_XxdklyUNkCV6JnueY,5995
test_unstructured/partition/test_xlsx.py,sha256=ZXMGX8BQs7l8YXdBLFdETymkwUhiYpei7y2qcd2_5oo,23877
test_unstructured/partition/test_xml.py,sha256=Udvqw3Zn_tpzTZ_B3ImHeckkhqbXPLB04IS4fw0HbB8,8745
test_unstructured/partition/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/partition/utils/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/partition/utils/__pycache__/test_config.cpython-310.pyc,,
test_unstructured/partition/utils/__pycache__/test_sorting.cpython-310.pyc,,
test_unstructured/partition/utils/__pycache__/test_xycut.cpython-310.pyc,,
test_unstructured/partition/utils/test_config.py,sha256=-L866HOHHfWN6rF85FxkETfbfpGOEcg23DbmcAX2Mes,2001
test_unstructured/partition/utils/test_sorting.py,sha256=-ViuWVKUNjJkobee1fcf_7kVViXuVnf0TYdapPnrv4E,5132
test_unstructured/partition/utils/test_xycut.py,sha256=I-VaPlTnxuPp8G0FC8iqUYGm0Z9UxtQ9ddtZh7q5HYo,6211
test_unstructured/staging/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
test_unstructured/staging/__pycache__/__init__.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_base.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_baseplate.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_datasaur.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_huggingface.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_label_box.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_label_studio.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_prodigy.cpython-310.pyc,,
test_unstructured/staging/__pycache__/test_weaviate.cpython-310.pyc,,
test_unstructured/staging/test_base.py,sha256=i8Na8NKFFKiEMjn-sIYAOlyzbUWD9oa8saX50BaQjQY,19976
test_unstructured/staging/test_baseplate.py,sha256=ACJ_OtLK_64e3xn3hS1jYZYApxaenETclphCzGyAFTc,2800
test_unstructured/staging/test_datasaur.py,sha256=jxn8jopADs1J7jRL-teWWtL0fD17wma0PAaAqoeDCkU,2176
test_unstructured/staging/test_huggingface.py,sha256=00MvpucyTEBFJebAVPxGlfOX4T3zl1esphH4W6JL-s8,2356
test_unstructured/staging/test_label_box.py,sha256=Pjbe5cPBWX2tlFk1YX1U29ujX-8slDYehQLd_1VC7K4,4335
test_unstructured/staging/test_label_studio.py,sha256=cH6oWhHHPGNAOBfH72T1xFGLQU-IakONZSskzDwlUts,10107
test_unstructured/staging/test_prodigy.py,sha256=MTFItzYNSnLNZ-ekMoNlGsfT4v9Hh8rFNFNwhtMvU-k,4020
test_unstructured/staging/test_weaviate.py,sha256=Mb0t_GrIzPjuTzYD3xdWMCu_PMCX0AfdRwn36hI6sdw,2214
test_unstructured/test_utils.py,sha256=v7ytk5UJccQ7OvaIOq8E6ioGSStcfG4HrPQD27xAQIU,10997
test_unstructured/unit_utils.py,sha256=ou0MBP2MStAx_ob-QGsuE5_JoDp_u6MUZoTEWFG7OAQ,8492
unstructured-0.18.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
unstructured-0.18.5.dist-info/LICENSE.md,sha256=SxkKP_62uIAKb9mb1eH7FH4Kn2aYT09fgjKpJt5PyTk,11360
unstructured-0.18.5.dist-info/METADATA,sha256=_dQgjP4tJl_Ex4ZXFD2oL_QcFyx6fFgchGwzwk1Q1PI,24586
unstructured-0.18.5.dist-info/RECORD,,
unstructured-0.18.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured-0.18.5.dist-info/WHEEL,sha256=tZoeGjtWxWRfdplE7E3d45VPlLNQnvbKiYnx7gwAy8A,92
unstructured-0.18.5.dist-info/top_level.txt,sha256=IVbYkzQJXExO4_PhBGUf5dc7OZZ75t9XYrjKn3KvodA,31
unstructured/__init__.py,sha256=SvwSYurR6AKi7Zp-JY0ZnR9D1QkIqtHM4FEdCgdAolM,77
unstructured/__pycache__/__init__.cpython-310.pyc,,
unstructured/__pycache__/__version__.cpython-310.pyc,,
unstructured/__pycache__/errors.cpython-310.pyc,,
unstructured/__pycache__/logger.cpython-310.pyc,,
unstructured/__pycache__/utils.cpython-310.pyc,,
unstructured/__version__.py,sha256=cw0O3qSe0_UKVC4fx5Uj61d3iCaEcs9ohzA2TwJn61E,43
unstructured/chunking/__init__.py,sha256=jvlh7MH_R3-v_5-ynDXcksd68w3ZejZcBbv5iJhLpOg,590
unstructured/chunking/__pycache__/__init__.cpython-310.pyc,,
unstructured/chunking/__pycache__/base.cpython-310.pyc,,
unstructured/chunking/__pycache__/basic.cpython-310.pyc,,
unstructured/chunking/__pycache__/dispatch.cpython-310.pyc,,
unstructured/chunking/__pycache__/title.cpython-310.pyc,,
unstructured/chunking/base.py,sha256=ehRDS7fvrgQs1PqnDblzrBECGFgf6Uu265m4FgBtXH4,57844
unstructured/chunking/basic.py,sha256=nIl41mrfV8pAxOEehwI20bKc-dwwNpBfRf8VWpKlw08,4249
unstructured/chunking/dispatch.py,sha256=hvZyn_K1vWFB2qIe6ndelDTr6YE7ZTUQSjidwzQmaew,5189
unstructured/chunking/title.py,sha256=UqB7-unPo49SeZVKG_rKeWJiESrREubv3Da3EE0yi6c,7594
unstructured/cleaners/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/cleaners/__pycache__/__init__.cpython-310.pyc,,
unstructured/cleaners/__pycache__/core.cpython-310.pyc,,
unstructured/cleaners/__pycache__/extract.cpython-310.pyc,,
unstructured/cleaners/__pycache__/translate.cpython-310.pyc,,
unstructured/cleaners/core.py,sha256=eHu58csaDtS2Xuhm163Mzt29lROpRTWzHsmmLFLGrI0,14646
unstructured/cleaners/extract.py,sha256=BbBYANbWz1BSYWaip2kAQ6GN86nVVr6HiyO5FRqjEHc,4339
unstructured/cleaners/translate.py,sha256=iyA3fSUtNAUnqiD-qP4RoqujNGaw9y07Mo9vztZkTJU,3288
unstructured/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/common/__pycache__/__init__.cpython-310.pyc,,
unstructured/common/__pycache__/html_table.cpython-310.pyc,,
unstructured/common/html_table.py,sha256=dTZAsVksgQlOopxwnppCqivwBqM0BLDKOervnSc0o0U,5824
unstructured/documents/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/documents/__pycache__/__init__.cpython-310.pyc,,
unstructured/documents/__pycache__/coordinates.cpython-310.pyc,,
unstructured/documents/__pycache__/elements.cpython-310.pyc,,
unstructured/documents/__pycache__/mappings.cpython-310.pyc,,
unstructured/documents/__pycache__/ontology.cpython-310.pyc,,
unstructured/documents/coordinates.py,sha256=LoHrK13Py3TkQL__Vobug0KmF1MYwMAXP81AFinuPJw,3937
unstructured/documents/elements.py,sha256=KvYTNzgopJNLF1cWImo-UDZlh5TnzdMnoOuRSwEtmMk,38825
unstructured/documents/mappings.py,sha256=23AAZIIuZcX1V07dHGdcZNfz9wkm8TWhwYvpGpZal4A,7097
unstructured/documents/ontology.py,sha256=zos2xnjGxPF8u3kywUy0qYtLxE17BpN30dyNrgolVek,24909
unstructured/embed/__init__.py,sha256=lqw55OZ3ibMbwPxdBcjPwbOPEDfjUguakuj7xN4bDdc,1046
unstructured/embed/__pycache__/__init__.cpython-310.pyc,,
unstructured/embed/__pycache__/bedrock.cpython-310.pyc,,
unstructured/embed/__pycache__/huggingface.cpython-310.pyc,,
unstructured/embed/__pycache__/interfaces.cpython-310.pyc,,
unstructured/embed/__pycache__/mixedbreadai.cpython-310.pyc,,
unstructured/embed/__pycache__/octoai.cpython-310.pyc,,
unstructured/embed/__pycache__/openai.cpython-310.pyc,,
unstructured/embed/__pycache__/vertexai.cpython-310.pyc,,
unstructured/embed/__pycache__/voyageai.cpython-310.pyc,,
unstructured/embed/bedrock.py,sha256=p8Pgm8PEYq_Z2c1f-D-LidtAyzxJ7uFzsWY52WcO-gM,2602
unstructured/embed/huggingface.py,sha256=GYTuTUwaZbfu6WFc5Nu3xLDXZxDdGXeZuPEfUIxe0C8,2465
unstructured/embed/interfaces.py,sha256=I5TDdbVC3x_yXXfBqSp1VOtVRBrLXwKbf4SgfTVhl2w,979
unstructured/embed/mixedbreadai.py,sha256=T2vibcjwIZExe64ko_zbY1t1izwuV6XvxkybLyHxbCU,5471
unstructured/embed/octoai.py,sha256=aFlgLhrTw-nrP_2gINO3AilJDDNiqCXObjtufjl0Yyw,2376
unstructured/embed/openai.py,sha256=cx_wBCENyBuQVJMG43xSTSXLSXm6_YH4YljeEWNSEI0,2284
unstructured/embed/vertexai.py,sha256=0PSdFuW_-5bR4UXrvzYBER7awTBqZtW8BVIBmaTXlW0,2821
unstructured/embed/voyageai.py,sha256=71_nypypcZWX_AudoETmAPx6q3arXmPZhvTEkIsjnjc,4317
unstructured/errors.py,sha256=os377OEQPhV5uyO0TpFH918R-lfGgbPlL_7T3Ubj0xM,503
unstructured/file_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/file_utils/__pycache__/__init__.cpython-310.pyc,,
unstructured/file_utils/__pycache__/encoding.cpython-310.pyc,,
unstructured/file_utils/__pycache__/file_conversion.cpython-310.pyc,,
unstructured/file_utils/__pycache__/filetype.cpython-310.pyc,,
unstructured/file_utils/__pycache__/google_filetype.cpython-310.pyc,,
unstructured/file_utils/__pycache__/model.cpython-310.pyc,,
unstructured/file_utils/__pycache__/ndjson.cpython-310.pyc,,
unstructured/file_utils/encoding.py,sha256=A1hRtcp-bZR6NpkCTAeHZE7b9o9NSTPyGCiwXasarH0,4420
unstructured/file_utils/file_conversion.py,sha256=39rOHoe8PT7jfC8wQ6QiadhEqh-SK_XGwkhZpsaTJA8,2663
unstructured/file_utils/filetype.py,sha256=NGc7g3Q0NuvD3OJF2C1hPJdkoHfRnD-3lDQkzy3NXCE,30606
unstructured/file_utils/google_filetype.py,sha256=YVspEkiiBrRUSGVeVbsavvLvTmizdy2e6TsjigXTSRU,468
unstructured/file_utils/model.py,sha256=Oqx_9Zq81zbyMBCmalJ4fNzWvYIbdG6Q_miNjOQR6mU,17292
unstructured/file_utils/ndjson.py,sha256=LezsF5LzmSPgTAE4PgGm0_DcPlgMDvPxRn2HONJm_yw,1974
unstructured/logger.py,sha256=aD9qsYFQBbyPSiuTfosXphv1k5EGcRnX7dAGB6sgb-g,686
unstructured/metrics/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/metrics/__pycache__/__init__.cpython-310.pyc,,
unstructured/metrics/__pycache__/element_type.cpython-310.pyc,,
unstructured/metrics/__pycache__/evaluate.cpython-310.pyc,,
unstructured/metrics/__pycache__/object_detection.cpython-310.pyc,,
unstructured/metrics/__pycache__/table_structure.cpython-310.pyc,,
unstructured/metrics/__pycache__/text_extraction.cpython-310.pyc,,
unstructured/metrics/__pycache__/utils.cpython-310.pyc,,
unstructured/metrics/element_type.py,sha256=OQuuB5Z6Xej2acJFowhTKSEJN0glyx69OCkYKocy8yg,3666
unstructured/metrics/evaluate.py,sha256=0vG4s4V7eAlXq9qVhXI85f4lfZoscvLWLRfAl3UPqWw,33711
unstructured/metrics/object_detection.py,sha256=t988hG16em75Rh0OXqo-3pLoEOPjmnVVt-3yYbOtUAE,31067
unstructured/metrics/table/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/metrics/table/__pycache__/__init__.cpython-310.pyc,,
unstructured/metrics/table/__pycache__/table_alignment.cpython-310.pyc,,
unstructured/metrics/table/__pycache__/table_eval.cpython-310.pyc,,
unstructured/metrics/table/__pycache__/table_extraction.cpython-310.pyc,,
unstructured/metrics/table/__pycache__/table_formats.cpython-310.pyc,,
unstructured/metrics/table/table_alignment.py,sha256=tyUG9XedxVppQydISoYfzlWlBsLHTB8yU38yko6nkts,7734
unstructured/metrics/table/table_eval.py,sha256=9SgpKV7JdSRFCTBEcx2M0sOHLsEVmt5LnyNhmruIerY,12458
unstructured/metrics/table/table_extraction.py,sha256=Qs9k8CLJJoAL5IMmiO-QLmpaL6QjoBFTNsj32mWb5Zo,9723
unstructured/metrics/table/table_formats.py,sha256=Jqrb-26zRtUVvwOyXw-CWLyV6vKrgFHVpZKD7TFzFjk,1383
unstructured/metrics/table_structure.py,sha256=uyWihfHyESx7aTr2A0YYqr7e5JkqEDVfrsSyI_pHcp8,1859
unstructured/metrics/text_extraction.py,sha256=QfHRfHPpyHkHt915GXCjLxy0PhlZrAjkoCgFVvz8utE,10068
unstructured/metrics/utils.py,sha256=TF_o-kZQ4NhZXn1JpC0l9a_ijE2SdzrpfTLn62UxW5A,8117
unstructured/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/models/__pycache__/__init__.cpython-310.pyc,,
unstructured/nlp/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/nlp/__pycache__/__init__.cpython-310.pyc,,
unstructured/nlp/__pycache__/english_words.cpython-310.pyc,,
unstructured/nlp/__pycache__/partition.cpython-310.pyc,,
unstructured/nlp/__pycache__/patterns.cpython-310.pyc,,
unstructured/nlp/__pycache__/tokenize.cpython-310.pyc,,
unstructured/nlp/english-words.txt,sha256=8fpk2f3iMm87qMppZMFAt1eWJcqOMALtt1YHE-fm7bY,4472047
unstructured/nlp/english_words.py,sha256=Ng2ozKrwF0Pw-qblYtBxxFOW9hT0eVL5uLqEgf0BHsw,701
unstructured/nlp/partition.py,sha256=8bTfn7O4Plk6FJ3-TmuTnxgjdZDnvExSGQpRYrddNJE,210
unstructured/nlp/patterns.py,sha256=Qtfdxyk3ieApTc9bWSZeFButZcnDdHvRjysC5SBxYeE,5664
unstructured/nlp/tokenize.py,sha256=6_Wo4C9npDTwTPG9r-_CgCKgVp53PjxTF0NI7tzKCBE,2313
unstructured/partition/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/partition/__pycache__/__init__.cpython-310.pyc,,
unstructured/partition/__pycache__/api.cpython-310.pyc,,
unstructured/partition/__pycache__/auto.cpython-310.pyc,,
unstructured/partition/__pycache__/csv.cpython-310.pyc,,
unstructured/partition/__pycache__/doc.cpython-310.pyc,,
unstructured/partition/__pycache__/docx.cpython-310.pyc,,
unstructured/partition/__pycache__/email.cpython-310.pyc,,
unstructured/partition/__pycache__/epub.cpython-310.pyc,,
unstructured/partition/__pycache__/image.cpython-310.pyc,,
unstructured/partition/__pycache__/json.cpython-310.pyc,,
unstructured/partition/__pycache__/md.cpython-310.pyc,,
unstructured/partition/__pycache__/model_init.cpython-310.pyc,,
unstructured/partition/__pycache__/msg.cpython-310.pyc,,
unstructured/partition/__pycache__/ndjson.cpython-310.pyc,,
unstructured/partition/__pycache__/odt.cpython-310.pyc,,
unstructured/partition/__pycache__/org.cpython-310.pyc,,
unstructured/partition/__pycache__/pdf.cpython-310.pyc,,
unstructured/partition/__pycache__/ppt.cpython-310.pyc,,
unstructured/partition/__pycache__/pptx.cpython-310.pyc,,
unstructured/partition/__pycache__/rst.cpython-310.pyc,,
unstructured/partition/__pycache__/rtf.cpython-310.pyc,,
unstructured/partition/__pycache__/strategies.cpython-310.pyc,,
unstructured/partition/__pycache__/text.cpython-310.pyc,,
unstructured/partition/__pycache__/text_type.cpython-310.pyc,,
unstructured/partition/__pycache__/tsv.cpython-310.pyc,,
unstructured/partition/__pycache__/xlsx.cpython-310.pyc,,
unstructured/partition/__pycache__/xml.cpython-310.pyc,,
unstructured/partition/api.py,sha256=wrXj2iUuVgWIBI94416gYbTdRHAxcAYMypjW_gpM9I8,13773
unstructured/partition/auto.py,sha256=v-oEPNrjMFpl9duB5FC6Vlf52HuTpX8GOaaQPFurQ1g,17426
unstructured/partition/common/__init__.py,sha256=s6_gBQedBRh0pGtrLb8qdlPdONTrq08GBWxFaGY4qpo,276
unstructured/partition/common/__pycache__/__init__.cpython-310.pyc,,
unstructured/partition/common/__pycache__/common.cpython-310.pyc,,
unstructured/partition/common/__pycache__/lang.cpython-310.pyc,,
unstructured/partition/common/__pycache__/metadata.cpython-310.pyc,,
unstructured/partition/common/common.py,sha256=X3r6buwxKp3BxruJfhXuiYJhgxCbRHaIXRr3MB8WVDQ,15483
unstructured/partition/common/lang.py,sha256=mdWHZUIddiWbqHsTpwzlHdQAYG8grNcZQMX0qXtFLMM,16687
unstructured/partition/common/metadata.py,sha256=WEWbeO7B066soq6Jhai_bygLfCmmCzO8LcfoIa6Abn4,11497
unstructured/partition/csv.py,sha256=YFz7Fom3H_FIk4oeGzM8F6Huup0uR_zwerq2oZOoEh8,6258
unstructured/partition/doc.py,sha256=ltrI9GISr4JNxktgVRpfMpyH55r1XK801P2g52EgRBA,4584
unstructured/partition/docx.py,sha256=JEgfV-bAuIpq1F4XwcGrt8GrtUfcV2zzMgMHrSmQ3o8,45431
unstructured/partition/email.py,sha256=y7rfaM9naMBtZ3uRpfkz9PIODNfLri0uQrhfy0MpQzQ,16881
unstructured/partition/epub.py,sha256=g5Gbqcy3JlC9sebsd84085Z9wrgzmJMF1tJdnSbebYY,2214
unstructured/partition/html/__init__.py,sha256=uFCKUengmT1m-Q_GkkZogEhv3MF7_rA9ahM0EF0S5dY,95
unstructured/partition/html/__pycache__/__init__.cpython-310.pyc,,
unstructured/partition/html/__pycache__/convert.cpython-310.pyc,,
unstructured/partition/html/__pycache__/html_utils.cpython-310.pyc,,
unstructured/partition/html/__pycache__/parser.cpython-310.pyc,,
unstructured/partition/html/__pycache__/partition.cpython-310.pyc,,
unstructured/partition/html/__pycache__/transformations.cpython-310.pyc,,
unstructured/partition/html/convert.py,sha256=vb4Y1EHw_GHIwhHevl4UI9YVOakpcPRHkKZharQ0lgM,11040
unstructured/partition/html/html_utils.py,sha256=AZm8KaPu5DCzuXZFqy3IVeTIk0lZ0-LJL8fSaT0DFRU,1064
unstructured/partition/html/parser.py,sha256=gh4nIbbaB34rgigzashoh37CFNBjqu38wBF_CE7kfbM,41326
unstructured/partition/html/partition.py,sha256=ijJxeN_nmcNeYwr0wqYEZrQVu9qQHshVqBtTEEIdF1I,10880
unstructured/partition/html/transformations.py,sha256=1JYCC5fEMJ-eod5F1qqFSd1oDfZj5AXWtZ1sgroRWWU,16969
unstructured/partition/image.py,sha256=ZEItuVEz4jw2AB4U-IDNX8sdD6rhLAkpgpO8-Z26oQU,5661
unstructured/partition/json.py,sha256=IehL7ciIdlMrV6VsBSBXYTuBBtTrq5Gp1ChS1W9ISKc,3101
unstructured/partition/md.py,sha256=NqGUQLkHiR36EkZU7r0D9UZRMlxqLEcCXr-2DM7aYng,2583
unstructured/partition/model_init.py,sha256=HdbUAn2jyMuJQG_s_vJT7polwh3epUx-H6SILETEhhA,586
unstructured/partition/msg.py,sha256=jVzxqj0G5LPKaUpnsQIHFlLcnhd1dLT1eEb8QxTkFlM,11475
unstructured/partition/ndjson.py,sha256=r4YLOQL37VNEvMAJhKK_sd9GdE-slAUrMPNsJF79tvU,3184
unstructured/partition/odt.py,sha256=PXZNY60Mu75Tqti393tivvP-n7R8mqNhINZMCpI0p_0,4560
unstructured/partition/org.py,sha256=8qQuG7QhWr49cgkTLlx23aKOQenyCO0KyiZqkmNPU9k,1615
unstructured/partition/pdf.py,sha256=80XSRAe7u4Fz__RW52NuxGTz768GXsgoQ94ZtnwjNWo,49716
unstructured/partition/pdf_image/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/partition/pdf_image/__pycache__/__init__.cpython-310.pyc,,
unstructured/partition/pdf_image/__pycache__/form_extraction.cpython-310.pyc,,
unstructured/partition/pdf_image/__pycache__/inference_utils.cpython-310.pyc,,
unstructured/partition/pdf_image/__pycache__/ocr.cpython-310.pyc,,
unstructured/partition/pdf_image/__pycache__/pdf_image_utils.cpython-310.pyc,,
unstructured/partition/pdf_image/__pycache__/pdfminer_processing.cpython-310.pyc,,
unstructured/partition/pdf_image/__pycache__/pdfminer_utils.cpython-310.pyc,,
unstructured/partition/pdf_image/__pycache__/pypdf_utils.cpython-310.pyc,,
unstructured/partition/pdf_image/analysis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/partition/pdf_image/analysis/__pycache__/__init__.cpython-310.pyc,,
unstructured/partition/pdf_image/analysis/__pycache__/bbox_visualisation.cpython-310.pyc,,
unstructured/partition/pdf_image/analysis/__pycache__/layout_dump.cpython-310.pyc,,
unstructured/partition/pdf_image/analysis/__pycache__/processor.cpython-310.pyc,,
unstructured/partition/pdf_image/analysis/__pycache__/tools.cpython-310.pyc,,
unstructured/partition/pdf_image/analysis/bbox_visualisation.py,sha256=S16RfvY04tNVkRTnb4P0PZNcoE0qghUrY6LtV4i2088,24648
unstructured/partition/pdf_image/analysis/layout_dump.py,sha256=ugMFTmJ_HSZXVYWG3LZpPJHd3tuowcE6OJktVCigzZ4,6778
unstructured/partition/pdf_image/analysis/processor.py,sha256=iQErLaNaLMqGggzPoRQxO9w2YVOnGsCGziRr3qsCKO4,434
unstructured/partition/pdf_image/analysis/tools.py,sha256=2Ue4pDdsiz7CXi1A-AQzxkpPQImqJE8Ag0YBR4WWmO4,7236
unstructured/partition/pdf_image/form_extraction.py,sha256=8yDrbMZEZbt6AaKuZvLl3YiRzp2rgiu3QN2a55CJlIo,369
unstructured/partition/pdf_image/inference_utils.py,sha256=wFAPfNL4OvhU35zZ6A94wPIej13SjcnbhfX4slbaAAU,3316
unstructured/partition/pdf_image/ocr.py,sha256=6MAggey9GPxM5TGth8nd9QDg99EveJOGvGVYLJJc6R4,20115
unstructured/partition/pdf_image/pdf_image_utils.py,sha256=gFwcxdIdZWqLqPIFSO2JP_2Md93NXEHuP7XrUfLL9bQ,16068
unstructured/partition/pdf_image/pdfminer_processing.py,sha256=L1nG3DPfe1Iw6rah4VipkMFVy_aEIYV6HZFQ76ZJkRI,42891
unstructured/partition/pdf_image/pdfminer_utils.py,sha256=PNWEA3cEZqEEFm9L3nOGumYobuQUuW0XLHWQHRiODjQ,5002
unstructured/partition/pdf_image/pypdf_utils.py,sha256=tE14XrOLRRNbhFwfdUXD9kLrD9BbSX3ipr7ggiB3AeU,409
unstructured/partition/ppt.py,sha256=YFj60k9OukrUlezMFzRHzmF7q9XYPRb8z2HcLAwqg-0,2659
unstructured/partition/pptx.py,sha256=pJZGMjaZf2TriA7V7yYytt4s9r8iFDfTslCA_EKlcQw,21616
unstructured/partition/rst.py,sha256=F96LxPOLvEAuKng5RN3xjBVozdv7vM_XROjheksuN6c,1637
unstructured/partition/rtf.py,sha256=NdrMLnFkEwmG1eSrsEv1XAfr1AhoNr6g1gobMoqqDAo,1637
unstructured/partition/strategies.py,sha256=rvSaAxzJqFxnmAkQPwMQI42d4vv0Iz7tqoDD2wz9y5s,4303
unstructured/partition/text.py,sha256=m1-BsxShkuy3W9BSz8v4_xzfASWUbovCMD_R5MSGqso,6867
unstructured/partition/text_type.py,sha256=iXOjumjIs754bPa6b9ddbNxUQUtinp6-LhGk3PpFsoE,11584
unstructured/partition/tsv.py,sha256=Ye6OYCW-IrvlEwiEtlf-q_Yf_1H7_jOlhxT4DHg6uwk,2050
unstructured/partition/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/partition/utils/__pycache__/__init__.cpython-310.pyc,,
unstructured/partition/utils/__pycache__/config.cpython-310.pyc,,
unstructured/partition/utils/__pycache__/constants.cpython-310.pyc,,
unstructured/partition/utils/__pycache__/sorting.cpython-310.pyc,,
unstructured/partition/utils/__pycache__/xycut.cpython-310.pyc,,
unstructured/partition/utils/config.py,sha256=IXuNCb3UbQgub-JZ5u5_6bS2y26CVx7NJANX0-ddHBY,8922
unstructured/partition/utils/constants.py,sha256=L-B0ZSMLyBtaj-AI5clyr8yOybtT2fHV2EQifnp_0e8,5666
unstructured/partition/utils/ocr_models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/partition/utils/ocr_models/__pycache__/__init__.cpython-310.pyc,,
unstructured/partition/utils/ocr_models/__pycache__/google_vision_ocr.cpython-310.pyc,,
unstructured/partition/utils/ocr_models/__pycache__/ocr_interface.cpython-310.pyc,,
unstructured/partition/utils/ocr_models/__pycache__/paddle_ocr.cpython-310.pyc,,
unstructured/partition/utils/ocr_models/__pycache__/tesseract_ocr.cpython-310.pyc,,
unstructured/partition/utils/ocr_models/google_vision_ocr.py,sha256=HMgSfMKya6GaalqRGjcWPpWafOTDa-Y-hUdLzWZv76s,4841
unstructured/partition/utils/ocr_models/ocr_interface.py,sha256=9XK4pwAwv5jDw4SqyMWEwaIeIh_JIwk7MSx0sHkYK9c,3466
unstructured/partition/utils/ocr_models/paddle_ocr.py,sha256=spLyxEMcvH4Tc9sgtCmTWvyGEqQjA7SIV9UHTzQOKHo,5770
unstructured/partition/utils/ocr_models/tesseract_ocr.py,sha256=_2tO1DWLGAQxVoqZkpmN0UuzmOY31EwfaecSAp0ASRU,10016
unstructured/partition/utils/sorting.py,sha256=_XLcxMNgM14z-Q5YPCb1-StE6o6ldAKSNe809rz3N8Q,8608
unstructured/partition/utils/xycut.py,sha256=K_4PaKNc7Vs3iL-PQ9FaP-r3gU2WahulOpaRYpP3rq0,10202
unstructured/partition/xlsx.py,sha256=bG_J7uaprhUX3nowf0s2R42AAKXbuCKWhImyEoiYAa8,17333
unstructured/partition/xml.py,sha256=32BIGMfU3cFtFkEv8s-2MCsKSGC8JvBUUg2RrbBBDmI,4416
unstructured/patches/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/patches/__pycache__/__init__.cpython-310.pyc,,
unstructured/patches/__pycache__/pdfminer.cpython-310.pyc,,
unstructured/patches/pdfminer.py,sha256=5JhA0ogzH4PplbEulN25XqwClsKldDCtfOcUplNvObk,2145
unstructured/py.typed,sha256=z3PGyU9Bs9Gq1-s8CjEJ8Y4Aev2MwVgsaVDwglLkTZw,118
unstructured/staging/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
unstructured/staging/__pycache__/__init__.cpython-310.pyc,,
unstructured/staging/__pycache__/argilla.cpython-310.pyc,,
unstructured/staging/__pycache__/base.cpython-310.pyc,,
unstructured/staging/__pycache__/baseplate.cpython-310.pyc,,
unstructured/staging/__pycache__/datasaur.cpython-310.pyc,,
unstructured/staging/__pycache__/huggingface.cpython-310.pyc,,
unstructured/staging/__pycache__/label_box.cpython-310.pyc,,
unstructured/staging/__pycache__/label_studio.cpython-310.pyc,,
unstructured/staging/__pycache__/prodigy.cpython-310.pyc,,
unstructured/staging/__pycache__/weaviate.cpython-310.pyc,,
unstructured/staging/argilla.py,sha256=DcG9QNYLTP8nN124OUcBB1lisFxQ-ruD4e_zJXEY97M,2292
unstructured/staging/base.py,sha256=dhpM2Wtww5tDL88CsTuGVHWkAsb9VjJeq1tX1CKbWjg,19828
unstructured/staging/baseplate.py,sha256=sTQ7umr6PlzjxrRwB2XDlFJGVUY1rv4VZ4z1UQ7q59g,1755
unstructured/staging/datasaur.py,sha256=7kG_XjY5YA0w1aPxR-PUSAllXNXKZdMop02Uu41wHc4,1417
unstructured/staging/huggingface.py,sha256=Nsej3wBydQDVvGNGPmsRZbeYOaMea8kRF949EY-kNxA,3838
unstructured/staging/label_box.py,sha256=uOaPT-3FtP_TkOSIjM2pUCZBxK8YgiDEwPblc1I8sSA,3855
unstructured/staging/label_studio.py,sha256=Ri7JfASfKfIgwdGaEF_tsBAVywfrr6_ReFZBjCnBLNA,5355
unstructured/staging/prodigy.py,sha256=wPMwatJ2lWr2_0qvlkv3MV55mkovHPz-ItUY0WcKxqw,3130
unstructured/staging/weaviate.py,sha256=hsl9OQ8Nwsx5GNrPI_-PQpNUX7lDCKBD0xImTMzKuHs,2607
unstructured/utils.py,sha256=7SvkKDLm1w_yV60-UAs0VMM9NMSz-cYBMnbTszhzky8,27930
