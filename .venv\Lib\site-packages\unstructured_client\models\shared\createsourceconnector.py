"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .azuresourceconnectorconfiginput import (
    AzureSourceConnectorConfigInput,
    AzureSourceConnectorConfigInputTypedDict,
)
from .boxsourceconnectorconfiginput import (
    BoxSourceConnectorConfigInput,
    BoxSourceConnectorConfigInputTypedDict,
)
from .confluencesourceconnectorconfiginput import (
    ConfluenceSourceConnectorConfigInput,
    ConfluenceSourceConnectorConfigInputTypedDict,
)
from .couchbasesourceconnectorconfiginput import (
    CouchbaseSourceConnectorConfigInput,
    CouchbaseSourceConnectorConfigInputTypedDict,
)
from .databricksvolumesconnectorconfiginput import (
    DatabricksVolumesConnectorConfigInput,
    DatabricksVolumesConnectorConfigInputTypedDict,
)
from .dropboxsourceconnectorconfiginput import (
    DropboxSourceConnectorConfigInput,
    DropboxSourceConnectorConfigInputTypedDict,
)
from .elasticsearchconnectorconfiginput import (
    ElasticsearchConnectorConfigInput,
    ElasticsearchConnectorConfigInputTypedDict,
)
from .gcssourceconnectorconfiginput import (
    GCSSourceConnectorConfigInput,
    GCSSourceConnectorConfigInputTypedDict,
)
from .googledrivesourceconnectorconfiginput import (
    GoogleDriveSourceConnectorConfigInput,
    GoogleDriveSourceConnectorConfigInputTypedDict,
)
from .jirasourceconnectorconfiginput import (
    JiraSourceConnectorConfigInput,
    JiraSourceConnectorConfigInputTypedDict,
)
from .kafkacloudsourceconnectorconfiginput import (
    KafkaCloudSourceConnectorConfigInput,
    KafkaCloudSourceConnectorConfigInputTypedDict,
)
from .mongodbconnectorconfiginput import (
    MongoDBConnectorConfigInput,
    MongoDBConnectorConfigInputTypedDict,
)
from .onedrivesourceconnectorconfiginput import (
    OneDriveSourceConnectorConfigInput,
    OneDriveSourceConnectorConfigInputTypedDict,
)
from .outlooksourceconnectorconfiginput import (
    OutlookSourceConnectorConfigInput,
    OutlookSourceConnectorConfigInputTypedDict,
)
from .postgressourceconnectorconfiginput import (
    PostgresSourceConnectorConfigInput,
    PostgresSourceConnectorConfigInputTypedDict,
)
from .s3sourceconnectorconfiginput import (
    S3SourceConnectorConfigInput,
    S3SourceConnectorConfigInputTypedDict,
)
from .salesforcesourceconnectorconfiginput import (
    SalesforceSourceConnectorConfigInput,
    SalesforceSourceConnectorConfigInputTypedDict,
)
from .sharepointsourceconnectorconfiginput import (
    SharePointSourceConnectorConfigInput,
    SharePointSourceConnectorConfigInputTypedDict,
)
from .snowflakesourceconnectorconfiginput import (
    SnowflakeSourceConnectorConfigInput,
    SnowflakeSourceConnectorConfigInputTypedDict,
)
from .sourceconnectortype import SourceConnectorType
from .zendesksourceconnectorconfiginput import (
    ZendeskSourceConnectorConfigInput,
    ZendeskSourceConnectorConfigInputTypedDict,
)
from typing import Union
from typing_extensions import TypeAliasType, TypedDict
from unstructured_client.types import BaseModel


CreateSourceConnectorConfigTypedDict = TypeAliasType(
    "CreateSourceConnectorConfigTypedDict",
    Union[
        MongoDBConnectorConfigInputTypedDict,
        BoxSourceConnectorConfigInputTypedDict,
        GCSSourceConnectorConfigInputTypedDict,
        ElasticsearchConnectorConfigInputTypedDict,
        DropboxSourceConnectorConfigInputTypedDict,
        GoogleDriveSourceConnectorConfigInputTypedDict,
        SalesforceSourceConnectorConfigInputTypedDict,
        ZendeskSourceConnectorConfigInputTypedDict,
        AzureSourceConnectorConfigInputTypedDict,
        S3SourceConnectorConfigInputTypedDict,
        DatabricksVolumesConnectorConfigInputTypedDict,
        KafkaCloudSourceConnectorConfigInputTypedDict,
        OneDriveSourceConnectorConfigInputTypedDict,
        OutlookSourceConnectorConfigInputTypedDict,
        SharePointSourceConnectorConfigInputTypedDict,
        CouchbaseSourceConnectorConfigInputTypedDict,
        PostgresSourceConnectorConfigInputTypedDict,
        JiraSourceConnectorConfigInputTypedDict,
        ConfluenceSourceConnectorConfigInputTypedDict,
        SnowflakeSourceConnectorConfigInputTypedDict,
    ],
)


CreateSourceConnectorConfig = TypeAliasType(
    "CreateSourceConnectorConfig",
    Union[
        MongoDBConnectorConfigInput,
        BoxSourceConnectorConfigInput,
        GCSSourceConnectorConfigInput,
        ElasticsearchConnectorConfigInput,
        DropboxSourceConnectorConfigInput,
        GoogleDriveSourceConnectorConfigInput,
        SalesforceSourceConnectorConfigInput,
        ZendeskSourceConnectorConfigInput,
        AzureSourceConnectorConfigInput,
        S3SourceConnectorConfigInput,
        DatabricksVolumesConnectorConfigInput,
        KafkaCloudSourceConnectorConfigInput,
        OneDriveSourceConnectorConfigInput,
        OutlookSourceConnectorConfigInput,
        SharePointSourceConnectorConfigInput,
        CouchbaseSourceConnectorConfigInput,
        PostgresSourceConnectorConfigInput,
        JiraSourceConnectorConfigInput,
        ConfluenceSourceConnectorConfigInput,
        SnowflakeSourceConnectorConfigInput,
    ],
)


class CreateSourceConnectorTypedDict(TypedDict):
    config: CreateSourceConnectorConfigTypedDict
    name: str
    type: SourceConnectorType


class CreateSourceConnector(BaseModel):
    config: CreateSourceConnectorConfig

    name: str

    type: SourceConnectorType
