"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class PineconeDestinationConnectorConfigTypedDict(TypedDict):
    api_key: str
    batch_size: int
    index_name: str
    namespace: str


class PineconeDestinationConnectorConfig(BaseModel):
    api_key: str

    batch_size: int

    index_name: str

    namespace: str
