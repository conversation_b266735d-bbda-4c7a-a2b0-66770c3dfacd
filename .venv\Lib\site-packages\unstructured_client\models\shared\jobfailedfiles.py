"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .failedfile import FailedFile, FailedFileTypedDict
from typing import List
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class JobFailedFilesTypedDict(TypedDict):
    failed_files: List[FailedFileTypedDict]


class JobFailedFiles(BaseModel):
    failed_files: List[FailedFile]
