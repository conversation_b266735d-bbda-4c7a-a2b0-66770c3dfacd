"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class IBMWatsonxS3DestinationConnectorConfigTypedDict(TypedDict):
    access_key_id: str
    catalog: str
    iam_api_key: str
    iceberg_endpoint: str
    max_retries: int
    max_retries_connection: int
    namespace: str
    object_storage_endpoint: str
    object_storage_region: str
    record_id_key: str
    secret_access_key: str
    table: str


class IBMWatsonxS3DestinationConnectorConfig(BaseModel):
    access_key_id: str

    catalog: str

    iam_api_key: str

    iceberg_endpoint: str

    max_retries: int

    max_retries_connection: int

    namespace: str

    object_storage_endpoint: str

    object_storage_region: str

    record_id_key: str

    secret_access_key: str

    table: str
