"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
import httpx
import pydantic
from pydantic import model_serializer
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.models.shared import (
    updateworkflow as shared_updateworkflow,
    workflowinformation as shared_workflowinformation,
)
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)
from unstructured_client.utils import (
    FieldMetadata,
    HeaderMetadata,
    PathParamMetadata,
    RequestMetadata,
)

UPDATE_WORKFLOW_SERVER_PLATFORM_API = "platform-api"
r"""Unstructured Platform API"""

UPDATE_WORKFLOW_SERVERS = {
    UPDATE_WORKFLOW_SERVER_PLATFORM_API: "https://platform.unstructuredapp.io/",
}


class UpdateWorkflowRequestTypedDict(TypedDict):
    update_workflow: shared_updateworkflow.UpdateWorkflowTypedDict
    workflow_id: str
    unstructured_api_key: NotRequired[Nullable[str]]


class UpdateWorkflowRequest(BaseModel):
    update_workflow: Annotated[
        shared_updateworkflow.UpdateWorkflow,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]

    workflow_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    unstructured_api_key: Annotated[
        OptionalNullable[str],
        pydantic.Field(alias="unstructured-api-key"),
        FieldMetadata(header=HeaderMetadata(style="simple", explode=False)),
    ] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["unstructured-api-key"]
        nullable_fields = ["unstructured-api-key"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m


class UpdateWorkflowResponseTypedDict(TypedDict):
    content_type: str
    r"""HTTP response content type for this operation"""
    status_code: int
    r"""HTTP response status code for this operation"""
    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""
    workflow_information: NotRequired[
        shared_workflowinformation.WorkflowInformationTypedDict
    ]
    r"""Successful Response"""


class UpdateWorkflowResponse(BaseModel):
    content_type: str
    r"""HTTP response content type for this operation"""

    status_code: int
    r"""HTTP response status code for this operation"""

    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""

    workflow_information: Optional[shared_workflowinformation.WorkflowInformation] = (
        None
    )
    r"""Successful Response"""
