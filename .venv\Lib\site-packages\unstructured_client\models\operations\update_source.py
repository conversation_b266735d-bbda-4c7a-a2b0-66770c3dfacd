"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
import httpx
import pydantic
from pydantic import model_serializer
from typing import Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.models.shared import (
    sourceconnectorinformation as shared_sourceconnectorinformation,
    updatesourceconnector as shared_updatesourceconnector,
)
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)
from unstructured_client.utils import (
    FieldMetadata,
    HeaderMetadata,
    PathParamMetadata,
    RequestMetadata,
)

UPDATE_SOURCE_SERVER_PLATFORM_API = "platform-api"
r"""Unstructured Platform API"""

UPDATE_SOURCE_SERVERS = {
    UPDATE_SOURCE_SERVER_PLATFORM_API: "https://platform.unstructuredapp.io/",
}


class UpdateSourceRequestTypedDict(TypedDict):
    update_source_connector: shared_updatesourceconnector.UpdateSourceConnectorTypedDict
    source_id: str
    unstructured_api_key: NotRequired[Nullable[str]]


class UpdateSourceRequest(BaseModel):
    update_source_connector: Annotated[
        shared_updatesourceconnector.UpdateSourceConnector,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]

    source_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    unstructured_api_key: Annotated[
        OptionalNullable[str],
        pydantic.Field(alias="unstructured-api-key"),
        FieldMetadata(header=HeaderMetadata(style="simple", explode=False)),
    ] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["unstructured-api-key"]
        nullable_fields = ["unstructured-api-key"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m


class UpdateSourceResponseTypedDict(TypedDict):
    content_type: str
    r"""HTTP response content type for this operation"""
    status_code: int
    r"""HTTP response status code for this operation"""
    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""
    source_connector_information: NotRequired[
        shared_sourceconnectorinformation.SourceConnectorInformationTypedDict
    ]
    r"""Successful Response"""


class UpdateSourceResponse(BaseModel):
    content_type: str
    r"""HTTP response content type for this operation"""

    status_code: int
    r"""HTTP response status code for this operation"""

    raw_response: httpx.Response
    r"""Raw HTTP response; suitable for custom response parsing"""

    source_connector_information: Optional[
        shared_sourceconnectorinformation.SourceConnectorInformation
    ] = None
    r"""Successful Response"""
