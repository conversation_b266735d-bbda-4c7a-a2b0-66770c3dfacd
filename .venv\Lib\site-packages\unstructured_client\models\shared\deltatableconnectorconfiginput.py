"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class DeltaTableConnectorConfigInputTypedDict(TypedDict):
    aws_access_key_id: str
    aws_region: str
    aws_secret_access_key: str
    table_uri: str


class DeltaTableConnectorConfigInput(BaseModel):
    aws_access_key_id: str

    aws_region: str

    aws_secret_access_key: str

    table_uri: str
