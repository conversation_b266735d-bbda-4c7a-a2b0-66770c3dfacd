"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class PineconeDestinationConnectorConfigInputTypedDict(TypedDict):
    api_key: str
    index_name: str
    namespace: str
    batch_size: NotRequired[int]


class PineconeDestinationConnectorConfigInput(BaseModel):
    api_key: str

    index_name: str

    namespace: str

    batch_size: Optional[int] = 50
