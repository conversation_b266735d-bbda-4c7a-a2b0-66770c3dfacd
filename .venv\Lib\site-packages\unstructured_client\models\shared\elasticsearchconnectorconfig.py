"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import List
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class ElasticsearchConnectorConfigTypedDict(TypedDict):
    es_api_key: str
    hosts: List[str]
    index_name: str


class ElasticsearchConnectorConfig(BaseModel):
    es_api_key: str

    hosts: List[str]

    index_name: str
