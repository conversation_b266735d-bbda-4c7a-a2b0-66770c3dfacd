"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
import pydantic
from pydantic import model_serializer
from typing import List, Optional
from typing_extensions import Annotated, NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class SnowflakeSourceConnectorConfigTypedDict(TypedDict):
    account: str
    database: str
    host: str
    id_column: str
    password: str
    role: str
    table_name: str
    user: str
    batch_size: NotRequired[int]
    fields: NotRequired[Nullable[List[str]]]
    port: NotRequired[int]
    schema_: NotRequired[str]


class SnowflakeSourceConnectorConfig(BaseModel):
    account: str

    database: str

    host: str

    id_column: str

    password: str

    role: str

    table_name: str

    user: str

    batch_size: Optional[int] = 100

    fields: OptionalNullable[List[str]] = UNSET

    port: Optional[int] = 443

    schema_: Annotated[Optional[str], pydantic.Field(alias="schema")] = None

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["batch_size", "fields", "port", "schema"]
        nullable_fields = ["fields"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
