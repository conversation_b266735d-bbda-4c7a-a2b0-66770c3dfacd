"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class DropboxSourceConnectorConfigTypedDict(TypedDict):
    recursive: bool
    remote_url: str
    token: str


class DropboxSourceConnectorConfig(BaseModel):
    recursive: bool

    remote_url: str

    token: str
