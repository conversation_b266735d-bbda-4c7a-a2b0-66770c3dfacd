"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .jobstatus import JobStatus
from .nodefilemetadata import NodeFileMetadata, NodeFileMetadataTypedDict
from .workflowjobtype import WorkflowJobType
from datetime import datetime
from pydantic import model_serializer
from typing import List, Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class JobInformationTypedDict(TypedDict):
    created_at: datetime
    id: str
    status: JobStatus
    workflow_id: str
    workflow_name: str
    input_file_ids: NotRequired[Nullable[List[str]]]
    job_type: NotRequired[WorkflowJobType]
    output_node_files: NotRequired[Nullable[List[NodeFileMetadataTypedDict]]]
    runtime: NotRequired[Nullable[str]]


class JobInformation(BaseModel):
    created_at: datetime

    id: str

    status: JobStatus

    workflow_id: str

    workflow_name: str

    input_file_ids: OptionalNullable[List[str]] = UNSET

    job_type: Optional[WorkflowJobType] = None

    output_node_files: OptionalNullable[List[NodeFileMetadata]] = UNSET

    runtime: OptionalNullable[str] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["input_file_ids", "job_type", "output_node_files", "runtime"]
        nullable_fields = ["input_file_ids", "output_node_files", "runtime"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
