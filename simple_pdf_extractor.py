#!/usr/bin/env python3
"""
Simple PDF Text Extractor using Unstructured (without LLM)
This is a basic version that just extracts text from PDFs
"""

import json
import os
import sys
from pathlib import Path
import argparse

# Import unstructured for PDF processing
from unstructured.partition.pdf import partition_pdf
from unstructured.documents.elements import Text, NarrativeText, Title, Table


class SimplePDFExtractor:
    """Simple PDF text extraction using Unstructured library"""
    
    def __init__(self):
        """Initialize the PDF extractor"""
        self.categories = [
            "Part Number",
            "Customer Part Number", 
            "Engineering Change Level",
            "Date",
            "Purchase Order No.",
            "Weight",
            "Checking Aid No.",
            "Checking Aid Engineering Change Level",
            "Organization Manufacturing Information (Vendor Name, Code and Address)",
            "Customer Submission Information",
            "PO buyer name",
            "Reason for Submission",
            "Level of Submission",
            "PSW should be signed by supplier",
            "The Production Rate column should be filled out"
        ]
    
    def extract_text_from_pdf(self, pdf_path: str) -> dict:
        """
        Extract text from PDF using unstructured library
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dictionary with extracted text and metadata
        """
        try:
            print(f"Extracting text from: {pdf_path}")
            
            # Use unstructured to partition the PDF
            elements = partition_pdf(
                filename=pdf_path,
                strategy="hi_res",  # High resolution for better accuracy
                infer_table_structure=True,  # Extract table structure
                extract_images_in_pdf=False,  # Skip images for now
                extract_image_block_types=["Image", "Table"]
            )
            
            # Organize elements by type
            elements_by_type = {}
            text_content = []
            
            for element in elements:
                element_type = type(element).__name__
                
                if element_type not in elements_by_type:
                    elements_by_type[element_type] = []
                
                if hasattr(element, 'text') and element.text.strip():
                    element_data = {
                        'text': element.text.strip(),
                        'type': element_type
                    }
                    
                    # Add metadata if available
                    if hasattr(element, 'metadata'):
                        element_data['metadata'] = element.metadata
                    
                    elements_by_type[element_type].append(element_data)
                    text_content.append(element.text.strip())
            
            full_text = "\n".join(text_content)
            
            result = {
                'pdf_path': pdf_path,
                'total_elements': len(elements),
                'elements_by_type': elements_by_type,
                'full_text': full_text,
                'text_length': len(full_text),
                'element_types': list(elements_by_type.keys())
            }
            
            print(f"Extracted {len(full_text)} characters from {len(elements)} elements")
            print(f"Element types found: {', '.join(elements_by_type.keys())}")
            
            return result
            
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return {'error': str(e)}
    
    def search_for_categories(self, text: str) -> dict:
        """
        Simple keyword search for categories in text
        This is a basic implementation without LLM
        
        Args:
            text: Input text to search
            
        Returns:
            Dictionary with found information
        """
        text_lower = text.lower()
        found_info = {}
        
        # Simple keyword patterns for each category
        patterns = {
            "Part Number": ["part number", "part no", "p/n", "part#"],
            "Customer Part Number": ["customer part", "cust part", "customer p/n"],
            "Engineering Change Level": ["engineering change", "ecl", "revision", "rev"],
            "Date": ["date", "dated"],
            "Purchase Order No.": ["purchase order", "po number", "p.o.", "po#"],
            "Weight": ["weight", "wt", "mass"],
            "Checking Aid No.": ["checking aid", "aid number", "aid no"],
            "Organization Manufacturing Information": ["vendor", "manufacturer", "supplier", "organization"],
            "Customer Submission Information": ["submission", "customer info"],
            "PO buyer name": ["buyer", "purchaser", "po buyer"],
            "Reason for Submission": ["reason", "purpose", "submission reason"],
            "Level of Submission": ["level", "submission level"],
            "PSW should be signed": ["psw", "signed", "signature"],
            "Production Rate": ["production rate", "rate", "capacity"]
        }
        
        # Search for patterns in text
        for category, keywords in patterns.items():
            found_values = []
            for keyword in keywords:
                if keyword in text_lower:
                    # Find the line containing the keyword
                    lines = text.split('\n')
                    for line in lines:
                        if keyword in line.lower():
                            found_values.append(line.strip())
            
            if found_values:
                found_info[category] = found_values[:3]  # Limit to first 3 matches
            else:
                found_info[category] = None
        
        return found_info
    
    def process_pdf(self, pdf_path: str, output_path: str = None) -> dict:
        """
        Complete pipeline: extract text from PDF and search for information
        
        Args:
            pdf_path: Path to PDF file
            output_path: Optional path to save JSON result
            
        Returns:
            Dictionary with extracted information
        """
        # Extract text from PDF
        extraction_result = self.extract_text_from_pdf(pdf_path)
        
        if 'error' in extraction_result:
            return extraction_result
        
        # Search for categories in the text
        found_info = self.search_for_categories(extraction_result['full_text'])
        
        # Combine results
        result = {
            'pdf_path': pdf_path,
            'extraction_method': 'unstructured_simple',
            'text_extraction': {
                'total_elements': extraction_result['total_elements'],
                'text_length': extraction_result['text_length'],
                'element_types': extraction_result['element_types']
            },
            'found_information': found_info,
            'full_text_preview': extraction_result['full_text'][:1000] + "..." if len(extraction_result['full_text']) > 1000 else extraction_result['full_text']
        }
        
        # Save to file if requested
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"Results saved to: {output_path}")
        
        return result


def main():
    """Main function to run the simple PDF extractor"""
    parser = argparse.ArgumentParser(description="Simple PDF text extraction using Unstructured")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("-o", "--output", help="Output JSON file path")
    
    args = parser.parse_args()
    
    # Check if PDF file exists
    if not os.path.exists(args.pdf_path):
        print(f"Error: PDF file not found: {args.pdf_path}")
        sys.exit(1)
    
    # Initialize extractor
    extractor = SimplePDFExtractor()
    
    # Process PDF
    result = extractor.process_pdf(args.pdf_path, args.output)
    
    # Print results
    if "error" not in result:
        print("\n" + "="*50)
        print("EXTRACTION RESULTS")
        print("="*50)
        print(f"PDF: {result['pdf_path']}")
        print(f"Text Length: {result['text_extraction']['text_length']} characters")
        print(f"Elements: {result['text_extraction']['total_elements']}")
        print(f"Element Types: {', '.join(result['text_extraction']['element_types'])}")
        
        print("\nFOUND INFORMATION:")
        print("-" * 30)
        for category, value in result['found_information'].items():
            if value:
                print(f"{category}: {value}")
            else:
                print(f"{category}: Not found")
        
        print("\nTEXT PREVIEW:")
        print("-" * 30)
        print(result['full_text_preview'])
        
    else:
        print(f"Error: {result['error']}")


if __name__ == "__main__":
    main()
