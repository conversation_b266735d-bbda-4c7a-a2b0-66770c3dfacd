"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class Neo4jDestinationConnectorConfigInputTypedDict(TypedDict):
    database: str
    password: str
    uri: str
    username: str
    batch_size: NotRequired[int]


class Neo4jDestinationConnectorConfigInput(BaseModel):
    database: str

    password: str

    uri: str

    username: str

    batch_size: Optional[int] = 100
