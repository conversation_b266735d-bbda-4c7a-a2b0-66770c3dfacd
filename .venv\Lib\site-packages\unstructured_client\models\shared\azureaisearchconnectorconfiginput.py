"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class AzureAISearchConnectorConfigInputTypedDict(TypedDict):
    endpoint: str
    index: str
    key: str


class AzureAISearchConnectorConfigInput(BaseModel):
    endpoint: str

    index: str

    key: str
