"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .encryptiontype import EncryptionType
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class SecretReferenceTypedDict(TypedDict):
    id: str
    type: NotRequired[EncryptionType]


class SecretReference(BaseModel):
    id: str

    type: Optional[EncryptionType] = None
