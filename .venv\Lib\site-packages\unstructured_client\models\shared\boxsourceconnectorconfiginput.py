"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class BoxSourceConnectorConfigInputTypedDict(TypedDict):
    box_app_config: str
    remote_url: str
    recursive: NotRequired[bool]


class BoxSourceConnectorConfigInput(BaseModel):
    box_app_config: str

    remote_url: str

    recursive: Optional[bool] = True
