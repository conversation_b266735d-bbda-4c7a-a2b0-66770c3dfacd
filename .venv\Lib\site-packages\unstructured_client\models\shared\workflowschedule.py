"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .crontabentry import CronTabEntry, CronTabEntryTypedDict
from typing import List
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class WorkflowScheduleTypedDict(TypedDict):
    crontab_entries: List[CronTabEntryTypedDict]


class WorkflowSchedule(BaseModel):
    crontab_entries: List[CronTabEntry]
