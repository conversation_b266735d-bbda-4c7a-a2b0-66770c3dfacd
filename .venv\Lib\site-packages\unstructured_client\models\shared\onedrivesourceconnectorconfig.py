"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class OneDriveSourceConnectorConfigTypedDict(TypedDict):
    authority_url: str
    client_cred: str
    client_id: str
    path: str
    recursive: bool
    tenant: str
    user_pname: str


class OneDriveSourceConnectorConfig(BaseModel):
    authority_url: str

    client_cred: str

    client_id: str

    path: str

    recursive: bool

    tenant: str

    user_pname: str
