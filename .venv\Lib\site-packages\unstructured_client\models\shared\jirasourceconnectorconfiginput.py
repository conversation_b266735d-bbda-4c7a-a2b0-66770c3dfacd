"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from pydantic import model_serializer
from typing import List
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class JiraSourceConnectorConfigInputTypedDict(TypedDict):
    url: str
    username: str
    boards: NotRequired[Nullable[List[str]]]
    cloud: NotRequired[Nullable[bool]]
    download_attachments: NotRequired[Nullable[bool]]
    issues: NotRequired[Nullable[List[str]]]
    password: NotRequired[Nullable[str]]
    projects: NotRequired[Nullable[List[str]]]
    status_filters: NotRequired[Nullable[List[str]]]
    token: NotRequired[Nullable[str]]


class JiraSourceConnectorConfigInput(BaseModel):
    url: str

    username: str

    boards: OptionalNullable[List[str]] = UNSET

    cloud: OptionalNullable[bool] = UNSET

    download_attachments: OptionalNullable[bool] = UNSET

    issues: OptionalNullable[List[str]] = UNSET

    password: OptionalNullable[str] = UNSET

    projects: OptionalNullable[List[str]] = UNSET

    status_filters: OptionalNullable[List[str]] = UNSET

    token: OptionalNullable[str] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "boards",
            "cloud",
            "download_attachments",
            "issues",
            "password",
            "projects",
            "status_filters",
            "token",
        ]
        nullable_fields = [
            "boards",
            "cloud",
            "download_attachments",
            "issues",
            "password",
            "projects",
            "status_filters",
            "token",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
