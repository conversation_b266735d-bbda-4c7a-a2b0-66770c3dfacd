"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from pydantic import model_serializer
from typing import List, Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import (
    BaseModel,
    Nullable,
    OptionalNullable,
    UNSET,
    UNSET_SENTINEL,
)


class ConfluenceSourceConnectorConfigTypedDict(TypedDict):
    cloud: bool
    max_num_of_docs_from_each_space: int
    max_num_of_spaces: int
    spaces: Nullable[List[str]]
    url: str
    username: str
    api_token: NotRequired[Nullable[str]]
    extract_files: NotRequired[bool]
    extract_images: NotRequired[bool]
    password: NotRequired[Nullable[str]]
    token: NotRequired[Nullable[str]]


class ConfluenceSourceConnectorConfig(BaseModel):
    cloud: bool

    max_num_of_docs_from_each_space: int

    max_num_of_spaces: int

    spaces: Nullable[List[str]]

    url: str

    username: str

    api_token: OptionalNullable[str] = UNSET

    extract_files: Optional[bool] = False

    extract_images: Optional[bool] = False

    password: OptionalNullable[str] = UNSET

    token: OptionalNullable[str] = UNSET

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "api_token",
            "extract_files",
            "extract_images",
            "password",
            "token",
        ]
        nullable_fields = ["api_token", "password", "spaces", "token"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
