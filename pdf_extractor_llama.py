#!/usr/bin/env python3
"""
PDF Text Extraction and Information Extraction using Unstructured and Llama 3.2
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import argparse

# Import unstructured for PDF processing
from unstructured.partition.pdf import partition_pdf
from unstructured.documents.elements import Text, NarrativeText, Title, Table

# Import for Llama 3.2 (using ollama or transformers)
try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    print("Warning: ollama not available. Install with: pip install ollama")

try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("Warning: transformers not available. Install with: pip install transformers torch")


class PDFExtractor:
    """PDF text extraction and information extraction using Unstructured and Llama 3.2"""
    
    def __init__(self, use_ollama: bool = True):
        """
        Initialize the PDF extractor
        
        Args:
            use_ollama: Whether to use Ollama (True) or Transformers (False) for Llama 3.2
        """
        self.use_ollama = use_ollama
        self.categories = [
            "Part Number",
            "Customer Part Number", 
            "Engineering Change Level",
            "Date",
            "Purchase Order No.",
            "Weight",
            "Checking Aid No.",
            "Checking Aid Engineering Change Level",
            "Organization Manufacturing Information (Vendor Name, Code and Address)",
            "Customer Submission Information",
            "PO buyer name",
            "Reason for Submission",
            "Level of Submission",
            "PSW should be signed by supplier",
            "The Production Rate column should be filled out"
        ]
        
        # Initialize Llama model if using transformers
        if not use_ollama and TRANSFORMERS_AVAILABLE:
            self._init_transformers_model()
    
    def _init_transformers_model(self):
        """Initialize Llama 3.2 model using transformers"""
        try:
            model_name = "meta-llama/Llama-3.2-3B-Instruct"  # You may need to use a different model
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto"
            )
            print(f"Loaded Llama model: {model_name}")
        except Exception as e:
            print(f"Error loading Llama model: {e}")
            print("Make sure you have access to the model and proper authentication")
            self.model = None
            self.tokenizer = None
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract text from PDF using unstructured library
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Extracted text as string
        """
        try:
            print(f"Extracting text from: {pdf_path}")
            
            # Use unstructured to partition the PDF
            elements = partition_pdf(
                filename=pdf_path,
                strategy="hi_res",  # High resolution for better accuracy
                infer_table_structure=True,  # Extract table structure
                extract_images_in_pdf=False,  # Skip images for now
                extract_image_block_types=["Image", "Table"]
            )
            
            # Combine all text elements
            text_content = []
            for element in elements:
                if hasattr(element, 'text') and element.text.strip():
                    text_content.append(element.text.strip())
            
            full_text = "\n".join(text_content)
            print(f"Extracted {len(full_text)} characters from PDF")
            
            return full_text
            
        except Exception as e:
            print(f"Error extracting text from PDF: {e}")
            return ""
    
    def extract_information_with_llama(self, text: str) -> Dict[str, Any]:
        """
        Extract specific information from text using Llama 3.2
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary with extracted information in JSON format
        """
        if self.use_ollama and OLLAMA_AVAILABLE:
            return self._extract_with_ollama(text)
        elif not self.use_ollama and TRANSFORMERS_AVAILABLE and self.model:
            return self._extract_with_transformers(text)
        else:
            print("No Llama model available. Please install ollama or transformers.")
            return self._create_empty_result()
    
    def _extract_with_ollama(self, text: str) -> Dict[str, Any]:
        """Extract information using Ollama"""
        try:
            prompt = self._create_extraction_prompt(text)
            
            response = ollama.chat(
                model='llama3.2',  # Make sure you have llama3.2 model pulled
                messages=[
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )
            
            # Parse the response
            result_text = response['message']['content']
            return self._parse_llama_response(result_text)
            
        except Exception as e:
            print(f"Error with Ollama: {e}")
            print("Make sure Ollama is running and llama3.2 model is available")
            print("Run: ollama pull llama3.2")
            return self._create_empty_result()
    
    def _extract_with_transformers(self, text: str) -> Dict[str, Any]:
        """Extract information using transformers"""
        try:
            prompt = self._create_extraction_prompt(text)
            
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=4096)
            
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=1000,
                    temperature=0.1,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            # Remove the prompt from the response
            result_text = response[len(prompt):].strip()
            
            return self._parse_llama_response(result_text)
            
        except Exception as e:
            print(f"Error with transformers: {e}")
            return self._create_empty_result()
    
    def _create_extraction_prompt(self, text: str) -> str:
        """Create prompt for Llama to extract information"""
        categories_str = "\n".join([f"- {cat}" for cat in self.categories])
        
        prompt = f"""
You are an expert document analyzer. Extract the following information from the provided text and return ONLY a valid JSON object.

Categories to extract:
{categories_str}

Instructions:
1. Analyze the text carefully
2. Extract values for each category if found
3. If a category is not found, use null as the value
4. Return ONLY a valid JSON object with the category names as keys
5. Do not include any explanations or additional text

Text to analyze:
{text[:4000]}  # Limit text to avoid token limits

JSON Response:
"""
        return prompt
    
    def _parse_llama_response(self, response: str) -> Dict[str, Any]:
        """Parse Llama response and extract JSON"""
        try:
            # Try to find JSON in the response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != 0:
                json_str = response[start_idx:end_idx]
                result = json.loads(json_str)
                return result
            else:
                print("No valid JSON found in response")
                return self._create_empty_result()
                
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            print(f"Response was: {response}")
            return self._create_empty_result()
    
    def _create_empty_result(self) -> Dict[str, Any]:
        """Create empty result with all categories set to null"""
        return {category: None for category in self.categories}
    
    def process_pdf(self, pdf_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Complete pipeline: extract text from PDF and extract information
        
        Args:
            pdf_path: Path to PDF file
            output_path: Optional path to save JSON result
            
        Returns:
            Dictionary with extracted information
        """
        # Extract text from PDF
        text = self.extract_text_from_pdf(pdf_path)
        
        if not text:
            print("No text extracted from PDF")
            return {"error": "No text extracted from PDF"}
        
        # Extract information using Llama
        extracted_info = self.extract_information_with_llama(text)
        
        # Add metadata
        result = {
            "pdf_path": pdf_path,
            "extracted_text_length": len(text),
            "extraction_method": "ollama" if self.use_ollama else "transformers",
            "extracted_information": extracted_info
        }
        
        # Save to file if requested
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"Results saved to: {output_path}")
        
        return result


def main():
    """Main function to run the PDF extractor"""
    parser = argparse.ArgumentParser(description="Extract information from PDF using Unstructured and Llama 3.2")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("-o", "--output", help="Output JSON file path")
    parser.add_argument("--use-transformers", action="store_true", 
                       help="Use transformers instead of Ollama for Llama 3.2")
    
    args = parser.parse_args()
    
    # Check if PDF file exists
    if not os.path.exists(args.pdf_path):
        print(f"Error: PDF file not found: {args.pdf_path}")
        sys.exit(1)
    
    # Initialize extractor
    use_ollama = not args.use_transformers
    extractor = PDFExtractor(use_ollama=use_ollama)
    
    # Process PDF
    result = extractor.process_pdf(args.pdf_path, args.output)
    
    # Print results
    if "error" not in result:
        print("\n" + "="*50)
        print("EXTRACTION RESULTS")
        print("="*50)
        print(json.dumps(result["extracted_information"], indent=2))
    else:
        print(f"Error: {result['error']}")


if __name__ == "__main__":
    main()
