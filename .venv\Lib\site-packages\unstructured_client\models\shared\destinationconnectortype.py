"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from enum import Enum


class DestinationConnectorType(str, Enum):
    ASTRADB = "astradb"
    AZURE_AI_SEARCH = "azure_ai_search"
    COUCHBASE = "couchbase"
    DATABRICKS_VOLUMES = "databricks_volumes"
    DATABRICKS_VOLUME_DELTA_TABLES = "databricks_volume_delta_tables"
    DELTA_TABLE = "delta_table"
    ELASTICSEARCH = "elasticsearch"
    GCS = "gcs"
    KAFKA_CLOUD = "kafka-cloud"
    MILVUS = "milvus"
    MONGODB = "mongodb"
    MOTHERDUCK = "motherduck"
    NEO4J = "neo4j"
    ONEDRIVE = "onedrive"
    PINECONE = "pinecone"
    POSTGRES = "postgres"
    REDIS = "redis"
    QDRANT_CLOUD = "qdrant-cloud"
    S3 = "s3"
    SNOWFLAKE = "snowflake"
    WEAVIATE_CLOUD = "weaviate-cloud"
    IBM_WATSONX_S3 = "ibm_watsonx_s3"
