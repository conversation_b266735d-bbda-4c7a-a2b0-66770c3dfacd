"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing_extensions import TypedDict
from unstructured_client.types import BaseModel


class PostgresDestinationConnectorConfigInputTypedDict(TypedDict):
    batch_size: int
    database: str
    host: str
    password: str
    port: int
    table_name: str
    username: str


class PostgresDestinationConnectorConfigInput(BaseModel):
    batch_size: int

    database: str

    host: str

    password: str

    port: int

    table_name: str

    username: str
