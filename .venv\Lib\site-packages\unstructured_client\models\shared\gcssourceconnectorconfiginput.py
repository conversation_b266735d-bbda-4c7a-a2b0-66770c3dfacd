"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Optional
from typing_extensions import NotRequired, TypedDict
from unstructured_client.types import BaseModel


class GCSSourceConnectorConfigInputTypedDict(TypedDict):
    remote_url: str
    service_account_key: str
    recursive: NotRequired[bool]


class GCSSourceConnectorConfigInput(BaseModel):
    remote_url: str

    service_account_key: str

    recursive: Optional[bool] = True
